{"secrets": {"fmp_api_key": "Z0FBQUFBQm9nbFZoUmd4MWROc2NXcy1OcV9VNTl6TldyYTdCNkVGZWRwMmZicnZWM0RuRlNhUDNpaVhEbTNDbFByTVgxTW9xaUVSU0JKVS15bVFzVmEwYmlVdkM0dlJqYU9CQThxUlg1UXQ4ZGo2ZHRtUkpoNng3Z3ZvckFqMl9qb21UMU5zdmc0QS0=", "alpaca_api_key": "Z0FBQUFBQm9nbFZoa0k0Z2JjZ2JtOFByeEI3X2djUlRuOTdqcEs3Z0c4aVhqeE1XN1VTQXp3d013b1YxQzVtY1F1NExvV1U2ZDl0NFBYSXg4d1Z4eWZ4SmVBVFdPVTNPZG9xOFF1Z1kzN3FyQmRCOVBvNE1QX2c9", "alpaca_secret_key": "Z0FBQUFBQm9nbFZodThBaFRsazZ1MHNCSlRkeko4ZkRqbk80a1FydUZFZFo1N3VvZk1HVUxCM09Gb0lRR21YdlROMGtuTTRZYXFFT3hfc0lEdHY1a2EwSkxhR2ZxMlgxVS1HV2pZQ0ZoNUNraDZrTlZSSFFEUnVlUEt5QmJkSVZGRjFjLUFsSFpjRVU=", "grok_api_key": "Z0FBQUFBQm9nbFZoRHhoaUNNZmZpREh0YV9IUVlLeUxNaFNzd2lJMDdLcXJacGd2TTFSMV9ubnBvYXFJQ1N1eDVpaG5uSGNONlBuZVRzZElkRkRQMGgxNUFpdTNGYWZEVHpCaEFyOEJkSVF2Y28yQmwwSVRuY3otcXpiMWswczQ2NDVGVS1CT1hZbUZKcEZuNy12emljSUZQdVRXZUk3RUN6UTA3YWVtOWlGRUdTY2UyV1dla1czeUlqYk1feTlka0VVOWlGR2JrVmRZ", "openai_api_key": "Z0FBQUFBQm9nbFZoM0gyc2R5YUpNVEdYTGNJUXpKOWREekszY2NqdDJzN1luZlFURkRzVU9JUGdZSWNnLXhJdDNEWHhRVzBRLXhPSzhFUWhONkxldkE0V2R5SElaYWV3WTM3TmtNVFNDcGk3aGFXcjk2aHJGUTJ3NmxRYk1POTl4YlJJcHpaRzQxaW5JcnVGRFg4MkI0cFFLRmdkR3d3dF81OU5lU0l5Um5SWEI0N0d5TGVtcVpyM0ZHMzR3bWNia09vNWh6WldIMWRKRFpnV1RfSDhjTV9jY2NuZHl5M1hFYnZiT2s1cXJ6UjRZc2VwZXc3TTBiZncxN2ljQjNHNGhWVlUtak5lR0xTLWl6N2NxR2lxWXBKUjlsZ1JoT0dKS2w1SUZmNEZaMllya3RTcUVJYWNQdTQ9", "polygon_api_key": "Z0FBQUFBQm9nbFZoLV9seDNhb1d4bVAzMnBtc0hUdlpSYWgtcjdzRE5JaHhHNGFHZHk2MXBIZmFNdVNCaU5kT0llcTNuZmpBWkJYc2tyVXB0TW5lYW1iQmJzR3JBQ2U4QlNtT0hXTndoNUpRQnpEdkVrWXY3Q1k9"}, "metadata": {"fmp_api_key": {"name": "fmp_api_key", "created_at": "2025-07-23T15:18:08.307218", "last_accessed": "2025-07-24T10:46:41.293306", "access_count": 28, "encrypted": true, "source": "setup"}, "alpaca_api_key": {"name": "alpaca_api_key", "created_at": "2025-07-23T15:18:08.309775", "last_accessed": "2025-07-24T10:46:41.294935", "access_count": 28, "encrypted": true, "source": "setup"}, "alpaca_secret_key": {"name": "alpaca_secret_key", "created_at": "2025-07-23T15:18:08.310325", "last_accessed": "2025-07-24T10:46:41.296534", "access_count": 28, "encrypted": true, "source": "setup"}, "grok_api_key": {"name": "grok_api_key", "created_at": "2025-07-23T15:18:08.310704", "last_accessed": "2025-07-24T10:46:41.298053", "access_count": 20, "encrypted": true, "source": "setup"}, "openai_api_key": {"name": "openai_api_key", "created_at": "2025-07-23T15:46:40.131346", "last_accessed": "2025-07-24T10:46:41.299792", "access_count": 27, "encrypted": true, "source": "environment"}, "polygon_api_key": {"name": "polygon_api_key", "created_at": "2025-07-24T09:40:52.897190", "last_accessed": "2025-07-24T10:46:41.301295", "access_count": 18, "encrypted": true, "source": "environment"}}, "version": "1.0", "created_at": "2025-07-24T10:46:41.301565"}