# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1NetworkPolicyEgressRule(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'ports': 'list[V1NetworkPolicyPort]',
        'to': 'list[V1NetworkPolicyPeer]'
    }

    attribute_map = {
        'ports': 'ports',
        'to': 'to'
    }

    def __init__(self, ports=None, to=None, local_vars_configuration=None):  # noqa: E501
        """V1NetworkPolicyEgressRule - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._ports = None
        self._to = None
        self.discriminator = None

        if ports is not None:
            self.ports = ports
        if to is not None:
            self.to = to

    @property
    def ports(self):
        """Gets the ports of this V1NetworkPolicyEgressRule.  # noqa: E501

        ports is a list of destination ports for outgoing traffic. Each item in this list is combined using a logical OR. If this field is empty or missing, this rule matches all ports (traffic not restricted by port). If this field is present and contains at least one item, then this rule allows traffic only if the traffic matches at least one port in the list.  # noqa: E501

        :return: The ports of this V1NetworkPolicyEgressRule.  # noqa: E501
        :rtype: list[V1NetworkPolicyPort]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this V1NetworkPolicyEgressRule.

        ports is a list of destination ports for outgoing traffic. Each item in this list is combined using a logical OR. If this field is empty or missing, this rule matches all ports (traffic not restricted by port). If this field is present and contains at least one item, then this rule allows traffic only if the traffic matches at least one port in the list.  # noqa: E501

        :param ports: The ports of this V1NetworkPolicyEgressRule.  # noqa: E501
        :type: list[V1NetworkPolicyPort]
        """

        self._ports = ports

    @property
    def to(self):
        """Gets the to of this V1NetworkPolicyEgressRule.  # noqa: E501

        to is a list of destinations for outgoing traffic of pods selected for this rule. Items in this list are combined using a logical OR operation. If this field is empty or missing, this rule matches all destinations (traffic not restricted by destination). If this field is present and contains at least one item, this rule allows traffic only if the traffic matches at least one item in the to list.  # noqa: E501

        :return: The to of this V1NetworkPolicyEgressRule.  # noqa: E501
        :rtype: list[V1NetworkPolicyPeer]
        """
        return self._to

    @to.setter
    def to(self, to):
        """Sets the to of this V1NetworkPolicyEgressRule.

        to is a list of destinations for outgoing traffic of pods selected for this rule. Items in this list are combined using a logical OR operation. If this field is empty or missing, this rule matches all destinations (traffic not restricted by destination). If this field is present and contains at least one item, this rule allows traffic only if the traffic matches at least one item in the to list.  # noqa: E501

        :param to: The to of this V1NetworkPolicyEgressRule.  # noqa: E501
        :type: list[V1NetworkPolicyPeer]
        """

        self._to = to

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1NetworkPolicyEgressRule):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1NetworkPolicyEgressRule):
            return True

        return self.to_dict() != other.to_dict()
