# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1NodeRuntimeHandlerFeatures(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'recursive_read_only_mounts': 'bool',
        'user_namespaces': 'bool'
    }

    attribute_map = {
        'recursive_read_only_mounts': 'recursiveReadOnlyMounts',
        'user_namespaces': 'userNamespaces'
    }

    def __init__(self, recursive_read_only_mounts=None, user_namespaces=None, local_vars_configuration=None):  # noqa: E501
        """V1NodeRuntimeHandlerFeatures - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._recursive_read_only_mounts = None
        self._user_namespaces = None
        self.discriminator = None

        if recursive_read_only_mounts is not None:
            self.recursive_read_only_mounts = recursive_read_only_mounts
        if user_namespaces is not None:
            self.user_namespaces = user_namespaces

    @property
    def recursive_read_only_mounts(self):
        """Gets the recursive_read_only_mounts of this V1NodeRuntimeHandlerFeatures.  # noqa: E501

        RecursiveReadOnlyMounts is set to true if the runtime handler supports RecursiveReadOnlyMounts.  # noqa: E501

        :return: The recursive_read_only_mounts of this V1NodeRuntimeHandlerFeatures.  # noqa: E501
        :rtype: bool
        """
        return self._recursive_read_only_mounts

    @recursive_read_only_mounts.setter
    def recursive_read_only_mounts(self, recursive_read_only_mounts):
        """Sets the recursive_read_only_mounts of this V1NodeRuntimeHandlerFeatures.

        RecursiveReadOnlyMounts is set to true if the runtime handler supports RecursiveReadOnlyMounts.  # noqa: E501

        :param recursive_read_only_mounts: The recursive_read_only_mounts of this V1NodeRuntimeHandlerFeatures.  # noqa: E501
        :type: bool
        """

        self._recursive_read_only_mounts = recursive_read_only_mounts

    @property
    def user_namespaces(self):
        """Gets the user_namespaces of this V1NodeRuntimeHandlerFeatures.  # noqa: E501

        UserNamespaces is set to true if the runtime handler supports UserNamespaces, including for volumes.  # noqa: E501

        :return: The user_namespaces of this V1NodeRuntimeHandlerFeatures.  # noqa: E501
        :rtype: bool
        """
        return self._user_namespaces

    @user_namespaces.setter
    def user_namespaces(self, user_namespaces):
        """Sets the user_namespaces of this V1NodeRuntimeHandlerFeatures.

        UserNamespaces is set to true if the runtime handler supports UserNamespaces, including for volumes.  # noqa: E501

        :param user_namespaces: The user_namespaces of this V1NodeRuntimeHandlerFeatures.  # noqa: E501
        :type: bool
        """

        self._user_namespaces = user_namespaces

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1NodeRuntimeHandlerFeatures):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1NodeRuntimeHandlerFeatures):
            return True

        return self.to_dict() != other.to_dict()
