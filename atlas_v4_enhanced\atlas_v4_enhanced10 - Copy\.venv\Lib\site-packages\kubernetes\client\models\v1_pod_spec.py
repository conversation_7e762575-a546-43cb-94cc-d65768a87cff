# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1PodSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'active_deadline_seconds': 'int',
        'affinity': 'V1Affinity',
        'automount_service_account_token': 'bool',
        'containers': 'list[V1Container]',
        'dns_config': 'V1PodDNSConfig',
        'dns_policy': 'str',
        'enable_service_links': 'bool',
        'ephemeral_containers': 'list[V1EphemeralContainer]',
        'host_aliases': 'list[V1HostAlias]',
        'host_ipc': 'bool',
        'host_network': 'bool',
        'host_pid': 'bool',
        'host_users': 'bool',
        'hostname': 'str',
        'image_pull_secrets': 'list[V1LocalObjectReference]',
        'init_containers': 'list[V1Container]',
        'node_name': 'str',
        'node_selector': 'dict(str, str)',
        'os': 'V1PodOS',
        'overhead': 'dict(str, str)',
        'preemption_policy': 'str',
        'priority': 'int',
        'priority_class_name': 'str',
        'readiness_gates': 'list[V1PodReadinessGate]',
        'resource_claims': 'list[V1PodResourceClaim]',
        'resources': 'V1ResourceRequirements',
        'restart_policy': 'str',
        'runtime_class_name': 'str',
        'scheduler_name': 'str',
        'scheduling_gates': 'list[V1PodSchedulingGate]',
        'security_context': 'V1PodSecurityContext',
        'service_account': 'str',
        'service_account_name': 'str',
        'set_hostname_as_fqdn': 'bool',
        'share_process_namespace': 'bool',
        'subdomain': 'str',
        'termination_grace_period_seconds': 'int',
        'tolerations': 'list[V1Toleration]',
        'topology_spread_constraints': 'list[V1TopologySpreadConstraint]',
        'volumes': 'list[V1Volume]'
    }

    attribute_map = {
        'active_deadline_seconds': 'activeDeadlineSeconds',
        'affinity': 'affinity',
        'automount_service_account_token': 'automountServiceAccountToken',
        'containers': 'containers',
        'dns_config': 'dnsConfig',
        'dns_policy': 'dnsPolicy',
        'enable_service_links': 'enableServiceLinks',
        'ephemeral_containers': 'ephemeralContainers',
        'host_aliases': 'hostAliases',
        'host_ipc': 'hostIPC',
        'host_network': 'hostNetwork',
        'host_pid': 'hostPID',
        'host_users': 'hostUsers',
        'hostname': 'hostname',
        'image_pull_secrets': 'imagePullSecrets',
        'init_containers': 'initContainers',
        'node_name': 'nodeName',
        'node_selector': 'nodeSelector',
        'os': 'os',
        'overhead': 'overhead',
        'preemption_policy': 'preemptionPolicy',
        'priority': 'priority',
        'priority_class_name': 'priorityClassName',
        'readiness_gates': 'readinessGates',
        'resource_claims': 'resourceClaims',
        'resources': 'resources',
        'restart_policy': 'restartPolicy',
        'runtime_class_name': 'runtimeClassName',
        'scheduler_name': 'schedulerName',
        'scheduling_gates': 'schedulingGates',
        'security_context': 'securityContext',
        'service_account': 'serviceAccount',
        'service_account_name': 'serviceAccountName',
        'set_hostname_as_fqdn': 'setHostnameAsFQDN',
        'share_process_namespace': 'shareProcessNamespace',
        'subdomain': 'subdomain',
        'termination_grace_period_seconds': 'terminationGracePeriodSeconds',
        'tolerations': 'tolerations',
        'topology_spread_constraints': 'topologySpreadConstraints',
        'volumes': 'volumes'
    }

    def __init__(self, active_deadline_seconds=None, affinity=None, automount_service_account_token=None, containers=None, dns_config=None, dns_policy=None, enable_service_links=None, ephemeral_containers=None, host_aliases=None, host_ipc=None, host_network=None, host_pid=None, host_users=None, hostname=None, image_pull_secrets=None, init_containers=None, node_name=None, node_selector=None, os=None, overhead=None, preemption_policy=None, priority=None, priority_class_name=None, readiness_gates=None, resource_claims=None, resources=None, restart_policy=None, runtime_class_name=None, scheduler_name=None, scheduling_gates=None, security_context=None, service_account=None, service_account_name=None, set_hostname_as_fqdn=None, share_process_namespace=None, subdomain=None, termination_grace_period_seconds=None, tolerations=None, topology_spread_constraints=None, volumes=None, local_vars_configuration=None):  # noqa: E501
        """V1PodSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._active_deadline_seconds = None
        self._affinity = None
        self._automount_service_account_token = None
        self._containers = None
        self._dns_config = None
        self._dns_policy = None
        self._enable_service_links = None
        self._ephemeral_containers = None
        self._host_aliases = None
        self._host_ipc = None
        self._host_network = None
        self._host_pid = None
        self._host_users = None
        self._hostname = None
        self._image_pull_secrets = None
        self._init_containers = None
        self._node_name = None
        self._node_selector = None
        self._os = None
        self._overhead = None
        self._preemption_policy = None
        self._priority = None
        self._priority_class_name = None
        self._readiness_gates = None
        self._resource_claims = None
        self._resources = None
        self._restart_policy = None
        self._runtime_class_name = None
        self._scheduler_name = None
        self._scheduling_gates = None
        self._security_context = None
        self._service_account = None
        self._service_account_name = None
        self._set_hostname_as_fqdn = None
        self._share_process_namespace = None
        self._subdomain = None
        self._termination_grace_period_seconds = None
        self._tolerations = None
        self._topology_spread_constraints = None
        self._volumes = None
        self.discriminator = None

        if active_deadline_seconds is not None:
            self.active_deadline_seconds = active_deadline_seconds
        if affinity is not None:
            self.affinity = affinity
        if automount_service_account_token is not None:
            self.automount_service_account_token = automount_service_account_token
        self.containers = containers
        if dns_config is not None:
            self.dns_config = dns_config
        if dns_policy is not None:
            self.dns_policy = dns_policy
        if enable_service_links is not None:
            self.enable_service_links = enable_service_links
        if ephemeral_containers is not None:
            self.ephemeral_containers = ephemeral_containers
        if host_aliases is not None:
            self.host_aliases = host_aliases
        if host_ipc is not None:
            self.host_ipc = host_ipc
        if host_network is not None:
            self.host_network = host_network
        if host_pid is not None:
            self.host_pid = host_pid
        if host_users is not None:
            self.host_users = host_users
        if hostname is not None:
            self.hostname = hostname
        if image_pull_secrets is not None:
            self.image_pull_secrets = image_pull_secrets
        if init_containers is not None:
            self.init_containers = init_containers
        if node_name is not None:
            self.node_name = node_name
        if node_selector is not None:
            self.node_selector = node_selector
        if os is not None:
            self.os = os
        if overhead is not None:
            self.overhead = overhead
        if preemption_policy is not None:
            self.preemption_policy = preemption_policy
        if priority is not None:
            self.priority = priority
        if priority_class_name is not None:
            self.priority_class_name = priority_class_name
        if readiness_gates is not None:
            self.readiness_gates = readiness_gates
        if resource_claims is not None:
            self.resource_claims = resource_claims
        if resources is not None:
            self.resources = resources
        if restart_policy is not None:
            self.restart_policy = restart_policy
        if runtime_class_name is not None:
            self.runtime_class_name = runtime_class_name
        if scheduler_name is not None:
            self.scheduler_name = scheduler_name
        if scheduling_gates is not None:
            self.scheduling_gates = scheduling_gates
        if security_context is not None:
            self.security_context = security_context
        if service_account is not None:
            self.service_account = service_account
        if service_account_name is not None:
            self.service_account_name = service_account_name
        if set_hostname_as_fqdn is not None:
            self.set_hostname_as_fqdn = set_hostname_as_fqdn
        if share_process_namespace is not None:
            self.share_process_namespace = share_process_namespace
        if subdomain is not None:
            self.subdomain = subdomain
        if termination_grace_period_seconds is not None:
            self.termination_grace_period_seconds = termination_grace_period_seconds
        if tolerations is not None:
            self.tolerations = tolerations
        if topology_spread_constraints is not None:
            self.topology_spread_constraints = topology_spread_constraints
        if volumes is not None:
            self.volumes = volumes

    @property
    def active_deadline_seconds(self):
        """Gets the active_deadline_seconds of this V1PodSpec.  # noqa: E501

        Optional duration in seconds the pod may be active on the node relative to StartTime before the system will actively try to mark it failed and kill associated containers. Value must be a positive integer.  # noqa: E501

        :return: The active_deadline_seconds of this V1PodSpec.  # noqa: E501
        :rtype: int
        """
        return self._active_deadline_seconds

    @active_deadline_seconds.setter
    def active_deadline_seconds(self, active_deadline_seconds):
        """Sets the active_deadline_seconds of this V1PodSpec.

        Optional duration in seconds the pod may be active on the node relative to StartTime before the system will actively try to mark it failed and kill associated containers. Value must be a positive integer.  # noqa: E501

        :param active_deadline_seconds: The active_deadline_seconds of this V1PodSpec.  # noqa: E501
        :type: int
        """

        self._active_deadline_seconds = active_deadline_seconds

    @property
    def affinity(self):
        """Gets the affinity of this V1PodSpec.  # noqa: E501


        :return: The affinity of this V1PodSpec.  # noqa: E501
        :rtype: V1Affinity
        """
        return self._affinity

    @affinity.setter
    def affinity(self, affinity):
        """Sets the affinity of this V1PodSpec.


        :param affinity: The affinity of this V1PodSpec.  # noqa: E501
        :type: V1Affinity
        """

        self._affinity = affinity

    @property
    def automount_service_account_token(self):
        """Gets the automount_service_account_token of this V1PodSpec.  # noqa: E501

        AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.  # noqa: E501

        :return: The automount_service_account_token of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._automount_service_account_token

    @automount_service_account_token.setter
    def automount_service_account_token(self, automount_service_account_token):
        """Sets the automount_service_account_token of this V1PodSpec.

        AutomountServiceAccountToken indicates whether a service account token should be automatically mounted.  # noqa: E501

        :param automount_service_account_token: The automount_service_account_token of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._automount_service_account_token = automount_service_account_token

    @property
    def containers(self):
        """Gets the containers of this V1PodSpec.  # noqa: E501

        List of containers belonging to the pod. Containers cannot currently be added or removed. There must be at least one container in a Pod. Cannot be updated.  # noqa: E501

        :return: The containers of this V1PodSpec.  # noqa: E501
        :rtype: list[V1Container]
        """
        return self._containers

    @containers.setter
    def containers(self, containers):
        """Sets the containers of this V1PodSpec.

        List of containers belonging to the pod. Containers cannot currently be added or removed. There must be at least one container in a Pod. Cannot be updated.  # noqa: E501

        :param containers: The containers of this V1PodSpec.  # noqa: E501
        :type: list[V1Container]
        """
        if self.local_vars_configuration.client_side_validation and containers is None:  # noqa: E501
            raise ValueError("Invalid value for `containers`, must not be `None`")  # noqa: E501

        self._containers = containers

    @property
    def dns_config(self):
        """Gets the dns_config of this V1PodSpec.  # noqa: E501


        :return: The dns_config of this V1PodSpec.  # noqa: E501
        :rtype: V1PodDNSConfig
        """
        return self._dns_config

    @dns_config.setter
    def dns_config(self, dns_config):
        """Sets the dns_config of this V1PodSpec.


        :param dns_config: The dns_config of this V1PodSpec.  # noqa: E501
        :type: V1PodDNSConfig
        """

        self._dns_config = dns_config

    @property
    def dns_policy(self):
        """Gets the dns_policy of this V1PodSpec.  # noqa: E501

        Set DNS policy for the pod. Defaults to \"ClusterFirst\". Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'. DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy. To have DNS options set along with hostNetwork, you have to specify DNS policy explicitly to 'ClusterFirstWithHostNet'.  # noqa: E501

        :return: The dns_policy of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._dns_policy

    @dns_policy.setter
    def dns_policy(self, dns_policy):
        """Sets the dns_policy of this V1PodSpec.

        Set DNS policy for the pod. Defaults to \"ClusterFirst\". Valid values are 'ClusterFirstWithHostNet', 'ClusterFirst', 'Default' or 'None'. DNS parameters given in DNSConfig will be merged with the policy selected with DNSPolicy. To have DNS options set along with hostNetwork, you have to specify DNS policy explicitly to 'ClusterFirstWithHostNet'.  # noqa: E501

        :param dns_policy: The dns_policy of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._dns_policy = dns_policy

    @property
    def enable_service_links(self):
        """Gets the enable_service_links of this V1PodSpec.  # noqa: E501

        EnableServiceLinks indicates whether information about services should be injected into pod's environment variables, matching the syntax of Docker links. Optional: Defaults to true.  # noqa: E501

        :return: The enable_service_links of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._enable_service_links

    @enable_service_links.setter
    def enable_service_links(self, enable_service_links):
        """Sets the enable_service_links of this V1PodSpec.

        EnableServiceLinks indicates whether information about services should be injected into pod's environment variables, matching the syntax of Docker links. Optional: Defaults to true.  # noqa: E501

        :param enable_service_links: The enable_service_links of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._enable_service_links = enable_service_links

    @property
    def ephemeral_containers(self):
        """Gets the ephemeral_containers of this V1PodSpec.  # noqa: E501

        List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing pod to perform user-initiated actions such as debugging. This list cannot be specified when creating a pod, and it cannot be modified by updating the pod spec. In order to add an ephemeral container to an existing pod, use the pod's ephemeralcontainers subresource.  # noqa: E501

        :return: The ephemeral_containers of this V1PodSpec.  # noqa: E501
        :rtype: list[V1EphemeralContainer]
        """
        return self._ephemeral_containers

    @ephemeral_containers.setter
    def ephemeral_containers(self, ephemeral_containers):
        """Sets the ephemeral_containers of this V1PodSpec.

        List of ephemeral containers run in this pod. Ephemeral containers may be run in an existing pod to perform user-initiated actions such as debugging. This list cannot be specified when creating a pod, and it cannot be modified by updating the pod spec. In order to add an ephemeral container to an existing pod, use the pod's ephemeralcontainers subresource.  # noqa: E501

        :param ephemeral_containers: The ephemeral_containers of this V1PodSpec.  # noqa: E501
        :type: list[V1EphemeralContainer]
        """

        self._ephemeral_containers = ephemeral_containers

    @property
    def host_aliases(self):
        """Gets the host_aliases of this V1PodSpec.  # noqa: E501

        HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts file if specified.  # noqa: E501

        :return: The host_aliases of this V1PodSpec.  # noqa: E501
        :rtype: list[V1HostAlias]
        """
        return self._host_aliases

    @host_aliases.setter
    def host_aliases(self, host_aliases):
        """Sets the host_aliases of this V1PodSpec.

        HostAliases is an optional list of hosts and IPs that will be injected into the pod's hosts file if specified.  # noqa: E501

        :param host_aliases: The host_aliases of this V1PodSpec.  # noqa: E501
        :type: list[V1HostAlias]
        """

        self._host_aliases = host_aliases

    @property
    def host_ipc(self):
        """Gets the host_ipc of this V1PodSpec.  # noqa: E501

        Use the host's ipc namespace. Optional: Default to false.  # noqa: E501

        :return: The host_ipc of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._host_ipc

    @host_ipc.setter
    def host_ipc(self, host_ipc):
        """Sets the host_ipc of this V1PodSpec.

        Use the host's ipc namespace. Optional: Default to false.  # noqa: E501

        :param host_ipc: The host_ipc of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._host_ipc = host_ipc

    @property
    def host_network(self):
        """Gets the host_network of this V1PodSpec.  # noqa: E501

        Host networking requested for this pod. Use the host's network namespace. If this option is set, the ports that will be used must be specified. Default to false.  # noqa: E501

        :return: The host_network of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._host_network

    @host_network.setter
    def host_network(self, host_network):
        """Sets the host_network of this V1PodSpec.

        Host networking requested for this pod. Use the host's network namespace. If this option is set, the ports that will be used must be specified. Default to false.  # noqa: E501

        :param host_network: The host_network of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._host_network = host_network

    @property
    def host_pid(self):
        """Gets the host_pid of this V1PodSpec.  # noqa: E501

        Use the host's pid namespace. Optional: Default to false.  # noqa: E501

        :return: The host_pid of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._host_pid

    @host_pid.setter
    def host_pid(self, host_pid):
        """Sets the host_pid of this V1PodSpec.

        Use the host's pid namespace. Optional: Default to false.  # noqa: E501

        :param host_pid: The host_pid of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._host_pid = host_pid

    @property
    def host_users(self):
        """Gets the host_users of this V1PodSpec.  # noqa: E501

        Use the host's user namespace. Optional: Default to true. If set to true or not present, the pod will be run in the host user namespace, useful for when the pod needs a feature only available to the host user namespace, such as loading a kernel module with CAP_SYS_MODULE. When set to false, a new userns is created for the pod. Setting false is useful for mitigating container breakout vulnerabilities even allowing users to run their containers as root without actually having root privileges on the host. This field is alpha-level and is only honored by servers that enable the UserNamespacesSupport feature.  # noqa: E501

        :return: The host_users of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._host_users

    @host_users.setter
    def host_users(self, host_users):
        """Sets the host_users of this V1PodSpec.

        Use the host's user namespace. Optional: Default to true. If set to true or not present, the pod will be run in the host user namespace, useful for when the pod needs a feature only available to the host user namespace, such as loading a kernel module with CAP_SYS_MODULE. When set to false, a new userns is created for the pod. Setting false is useful for mitigating container breakout vulnerabilities even allowing users to run their containers as root without actually having root privileges on the host. This field is alpha-level and is only honored by servers that enable the UserNamespacesSupport feature.  # noqa: E501

        :param host_users: The host_users of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._host_users = host_users

    @property
    def hostname(self):
        """Gets the hostname of this V1PodSpec.  # noqa: E501

        Specifies the hostname of the Pod If not specified, the pod's hostname will be set to a system-defined value.  # noqa: E501

        :return: The hostname of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this V1PodSpec.

        Specifies the hostname of the Pod If not specified, the pod's hostname will be set to a system-defined value.  # noqa: E501

        :param hostname: The hostname of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def image_pull_secrets(self):
        """Gets the image_pull_secrets of this V1PodSpec.  # noqa: E501

        ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec. If specified, these secrets will be passed to individual puller implementations for them to use. More info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod  # noqa: E501

        :return: The image_pull_secrets of this V1PodSpec.  # noqa: E501
        :rtype: list[V1LocalObjectReference]
        """
        return self._image_pull_secrets

    @image_pull_secrets.setter
    def image_pull_secrets(self, image_pull_secrets):
        """Sets the image_pull_secrets of this V1PodSpec.

        ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images used by this PodSpec. If specified, these secrets will be passed to individual puller implementations for them to use. More info: https://kubernetes.io/docs/concepts/containers/images#specifying-imagepullsecrets-on-a-pod  # noqa: E501

        :param image_pull_secrets: The image_pull_secrets of this V1PodSpec.  # noqa: E501
        :type: list[V1LocalObjectReference]
        """

        self._image_pull_secrets = image_pull_secrets

    @property
    def init_containers(self):
        """Gets the init_containers of this V1PodSpec.  # noqa: E501

        List of initialization containers belonging to the pod. Init containers are executed in order prior to containers being started. If any init container fails, the pod is considered to have failed and is handled according to its restartPolicy. The name for an init container or normal container must be unique among all containers. Init containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes. The resourceRequirements of an init container are taken into account during scheduling by finding the highest request/limit for each resource type, and then using the max of that value or the sum of the normal containers. Limits are applied to init containers in a similar fashion. Init containers cannot currently be added or removed. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/  # noqa: E501

        :return: The init_containers of this V1PodSpec.  # noqa: E501
        :rtype: list[V1Container]
        """
        return self._init_containers

    @init_containers.setter
    def init_containers(self, init_containers):
        """Sets the init_containers of this V1PodSpec.

        List of initialization containers belonging to the pod. Init containers are executed in order prior to containers being started. If any init container fails, the pod is considered to have failed and is handled according to its restartPolicy. The name for an init container or normal container must be unique among all containers. Init containers may not have Lifecycle actions, Readiness probes, Liveness probes, or Startup probes. The resourceRequirements of an init container are taken into account during scheduling by finding the highest request/limit for each resource type, and then using the max of that value or the sum of the normal containers. Limits are applied to init containers in a similar fashion. Init containers cannot currently be added or removed. Cannot be updated. More info: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/  # noqa: E501

        :param init_containers: The init_containers of this V1PodSpec.  # noqa: E501
        :type: list[V1Container]
        """

        self._init_containers = init_containers

    @property
    def node_name(self):
        """Gets the node_name of this V1PodSpec.  # noqa: E501

        NodeName indicates in which node this pod is scheduled. If empty, this pod is a candidate for scheduling by the scheduler defined in schedulerName. Once this field is set, the kubelet for this node becomes responsible for the lifecycle of this pod. This field should not be used to express a desire for the pod to be scheduled on a specific node. https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodename  # noqa: E501

        :return: The node_name of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this V1PodSpec.

        NodeName indicates in which node this pod is scheduled. If empty, this pod is a candidate for scheduling by the scheduler defined in schedulerName. Once this field is set, the kubelet for this node becomes responsible for the lifecycle of this pod. This field should not be used to express a desire for the pod to be scheduled on a specific node. https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodename  # noqa: E501

        :param node_name: The node_name of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def node_selector(self):
        """Gets the node_selector of this V1PodSpec.  # noqa: E501

        NodeSelector is a selector which must be true for the pod to fit on a node. Selector which must match a node's labels for the pod to be scheduled on that node. More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/  # noqa: E501

        :return: The node_selector of this V1PodSpec.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._node_selector

    @node_selector.setter
    def node_selector(self, node_selector):
        """Sets the node_selector of this V1PodSpec.

        NodeSelector is a selector which must be true for the pod to fit on a node. Selector which must match a node's labels for the pod to be scheduled on that node. More info: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/  # noqa: E501

        :param node_selector: The node_selector of this V1PodSpec.  # noqa: E501
        :type: dict(str, str)
        """

        self._node_selector = node_selector

    @property
    def os(self):
        """Gets the os of this V1PodSpec.  # noqa: E501


        :return: The os of this V1PodSpec.  # noqa: E501
        :rtype: V1PodOS
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this V1PodSpec.


        :param os: The os of this V1PodSpec.  # noqa: E501
        :type: V1PodOS
        """

        self._os = os

    @property
    def overhead(self):
        """Gets the overhead of this V1PodSpec.  # noqa: E501

        Overhead represents the resource overhead associated with running a pod for a given RuntimeClass. This field will be autopopulated at admission time by the RuntimeClass admission controller. If the RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests. The RuntimeClass admission controller will reject Pod create requests which have the overhead already set. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value defined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero. More info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md  # noqa: E501

        :return: The overhead of this V1PodSpec.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._overhead

    @overhead.setter
    def overhead(self, overhead):
        """Sets the overhead of this V1PodSpec.

        Overhead represents the resource overhead associated with running a pod for a given RuntimeClass. This field will be autopopulated at admission time by the RuntimeClass admission controller. If the RuntimeClass admission controller is enabled, overhead must not be set in Pod create requests. The RuntimeClass admission controller will reject Pod create requests which have the overhead already set. If RuntimeClass is configured and selected in the PodSpec, Overhead will be set to the value defined in the corresponding RuntimeClass, otherwise it will remain unset and treated as zero. More info: https://git.k8s.io/enhancements/keps/sig-node/688-pod-overhead/README.md  # noqa: E501

        :param overhead: The overhead of this V1PodSpec.  # noqa: E501
        :type: dict(str, str)
        """

        self._overhead = overhead

    @property
    def preemption_policy(self):
        """Gets the preemption_policy of this V1PodSpec.  # noqa: E501

        PreemptionPolicy is the Policy for preempting pods with lower priority. One of Never, PreemptLowerPriority. Defaults to PreemptLowerPriority if unset.  # noqa: E501

        :return: The preemption_policy of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._preemption_policy

    @preemption_policy.setter
    def preemption_policy(self, preemption_policy):
        """Sets the preemption_policy of this V1PodSpec.

        PreemptionPolicy is the Policy for preempting pods with lower priority. One of Never, PreemptLowerPriority. Defaults to PreemptLowerPriority if unset.  # noqa: E501

        :param preemption_policy: The preemption_policy of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._preemption_policy = preemption_policy

    @property
    def priority(self):
        """Gets the priority of this V1PodSpec.  # noqa: E501

        The priority value. Various system components use this field to find the priority of the pod. When Priority Admission Controller is enabled, it prevents users from setting this field. The admission controller populates this field from PriorityClassName. The higher the value, the higher the priority.  # noqa: E501

        :return: The priority of this V1PodSpec.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this V1PodSpec.

        The priority value. Various system components use this field to find the priority of the pod. When Priority Admission Controller is enabled, it prevents users from setting this field. The admission controller populates this field from PriorityClassName. The higher the value, the higher the priority.  # noqa: E501

        :param priority: The priority of this V1PodSpec.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def priority_class_name(self):
        """Gets the priority_class_name of this V1PodSpec.  # noqa: E501

        If specified, indicates the pod's priority. \"system-node-critical\" and \"system-cluster-critical\" are two special keywords which indicate the highest priorities with the former being the highest priority. Any other name must be defined by creating a PriorityClass object with that name. If not specified, the pod priority will be default or zero if there is no default.  # noqa: E501

        :return: The priority_class_name of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._priority_class_name

    @priority_class_name.setter
    def priority_class_name(self, priority_class_name):
        """Sets the priority_class_name of this V1PodSpec.

        If specified, indicates the pod's priority. \"system-node-critical\" and \"system-cluster-critical\" are two special keywords which indicate the highest priorities with the former being the highest priority. Any other name must be defined by creating a PriorityClass object with that name. If not specified, the pod priority will be default or zero if there is no default.  # noqa: E501

        :param priority_class_name: The priority_class_name of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._priority_class_name = priority_class_name

    @property
    def readiness_gates(self):
        """Gets the readiness_gates of this V1PodSpec.  # noqa: E501

        If specified, all readiness gates will be evaluated for pod readiness. A pod is ready when all its containers are ready AND all conditions specified in the readiness gates have status equal to \"True\" More info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates  # noqa: E501

        :return: The readiness_gates of this V1PodSpec.  # noqa: E501
        :rtype: list[V1PodReadinessGate]
        """
        return self._readiness_gates

    @readiness_gates.setter
    def readiness_gates(self, readiness_gates):
        """Sets the readiness_gates of this V1PodSpec.

        If specified, all readiness gates will be evaluated for pod readiness. A pod is ready when all its containers are ready AND all conditions specified in the readiness gates have status equal to \"True\" More info: https://git.k8s.io/enhancements/keps/sig-network/580-pod-readiness-gates  # noqa: E501

        :param readiness_gates: The readiness_gates of this V1PodSpec.  # noqa: E501
        :type: list[V1PodReadinessGate]
        """

        self._readiness_gates = readiness_gates

    @property
    def resource_claims(self):
        """Gets the resource_claims of this V1PodSpec.  # noqa: E501

        ResourceClaims defines which ResourceClaims must be allocated and reserved before the Pod is allowed to start. The resources will be made available to those containers which consume them by name.  This is an alpha field and requires enabling the DynamicResourceAllocation feature gate.  This field is immutable.  # noqa: E501

        :return: The resource_claims of this V1PodSpec.  # noqa: E501
        :rtype: list[V1PodResourceClaim]
        """
        return self._resource_claims

    @resource_claims.setter
    def resource_claims(self, resource_claims):
        """Sets the resource_claims of this V1PodSpec.

        ResourceClaims defines which ResourceClaims must be allocated and reserved before the Pod is allowed to start. The resources will be made available to those containers which consume them by name.  This is an alpha field and requires enabling the DynamicResourceAllocation feature gate.  This field is immutable.  # noqa: E501

        :param resource_claims: The resource_claims of this V1PodSpec.  # noqa: E501
        :type: list[V1PodResourceClaim]
        """

        self._resource_claims = resource_claims

    @property
    def resources(self):
        """Gets the resources of this V1PodSpec.  # noqa: E501


        :return: The resources of this V1PodSpec.  # noqa: E501
        :rtype: V1ResourceRequirements
        """
        return self._resources

    @resources.setter
    def resources(self, resources):
        """Sets the resources of this V1PodSpec.


        :param resources: The resources of this V1PodSpec.  # noqa: E501
        :type: V1ResourceRequirements
        """

        self._resources = resources

    @property
    def restart_policy(self):
        """Gets the restart_policy of this V1PodSpec.  # noqa: E501

        Restart policy for all containers within the pod. One of Always, OnFailure, Never. In some contexts, only a subset of those values may be permitted. Default to Always. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy  # noqa: E501

        :return: The restart_policy of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._restart_policy

    @restart_policy.setter
    def restart_policy(self, restart_policy):
        """Sets the restart_policy of this V1PodSpec.

        Restart policy for all containers within the pod. One of Always, OnFailure, Never. In some contexts, only a subset of those values may be permitted. Default to Always. More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#restart-policy  # noqa: E501

        :param restart_policy: The restart_policy of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._restart_policy = restart_policy

    @property
    def runtime_class_name(self):
        """Gets the runtime_class_name of this V1PodSpec.  # noqa: E501

        RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used to run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run. If unset or empty, the \"legacy\" RuntimeClass will be used, which is an implicit class with an empty definition that uses the default runtime handler. More info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class  # noqa: E501

        :return: The runtime_class_name of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._runtime_class_name

    @runtime_class_name.setter
    def runtime_class_name(self, runtime_class_name):
        """Sets the runtime_class_name of this V1PodSpec.

        RuntimeClassName refers to a RuntimeClass object in the node.k8s.io group, which should be used to run this pod.  If no RuntimeClass resource matches the named class, the pod will not be run. If unset or empty, the \"legacy\" RuntimeClass will be used, which is an implicit class with an empty definition that uses the default runtime handler. More info: https://git.k8s.io/enhancements/keps/sig-node/585-runtime-class  # noqa: E501

        :param runtime_class_name: The runtime_class_name of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._runtime_class_name = runtime_class_name

    @property
    def scheduler_name(self):
        """Gets the scheduler_name of this V1PodSpec.  # noqa: E501

        If specified, the pod will be dispatched by specified scheduler. If not specified, the pod will be dispatched by default scheduler.  # noqa: E501

        :return: The scheduler_name of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._scheduler_name

    @scheduler_name.setter
    def scheduler_name(self, scheduler_name):
        """Sets the scheduler_name of this V1PodSpec.

        If specified, the pod will be dispatched by specified scheduler. If not specified, the pod will be dispatched by default scheduler.  # noqa: E501

        :param scheduler_name: The scheduler_name of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._scheduler_name = scheduler_name

    @property
    def scheduling_gates(self):
        """Gets the scheduling_gates of this V1PodSpec.  # noqa: E501

        SchedulingGates is an opaque list of values that if specified will block scheduling the pod. If schedulingGates is not empty, the pod will stay in the SchedulingGated state and the scheduler will not attempt to schedule the pod.  SchedulingGates can only be set at pod creation time, and be removed only afterwards.  # noqa: E501

        :return: The scheduling_gates of this V1PodSpec.  # noqa: E501
        :rtype: list[V1PodSchedulingGate]
        """
        return self._scheduling_gates

    @scheduling_gates.setter
    def scheduling_gates(self, scheduling_gates):
        """Sets the scheduling_gates of this V1PodSpec.

        SchedulingGates is an opaque list of values that if specified will block scheduling the pod. If schedulingGates is not empty, the pod will stay in the SchedulingGated state and the scheduler will not attempt to schedule the pod.  SchedulingGates can only be set at pod creation time, and be removed only afterwards.  # noqa: E501

        :param scheduling_gates: The scheduling_gates of this V1PodSpec.  # noqa: E501
        :type: list[V1PodSchedulingGate]
        """

        self._scheduling_gates = scheduling_gates

    @property
    def security_context(self):
        """Gets the security_context of this V1PodSpec.  # noqa: E501


        :return: The security_context of this V1PodSpec.  # noqa: E501
        :rtype: V1PodSecurityContext
        """
        return self._security_context

    @security_context.setter
    def security_context(self, security_context):
        """Sets the security_context of this V1PodSpec.


        :param security_context: The security_context of this V1PodSpec.  # noqa: E501
        :type: V1PodSecurityContext
        """

        self._security_context = security_context

    @property
    def service_account(self):
        """Gets the service_account of this V1PodSpec.  # noqa: E501

        DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.  # noqa: E501

        :return: The service_account of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._service_account

    @service_account.setter
    def service_account(self, service_account):
        """Sets the service_account of this V1PodSpec.

        DeprecatedServiceAccount is a deprecated alias for ServiceAccountName. Deprecated: Use serviceAccountName instead.  # noqa: E501

        :param service_account: The service_account of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._service_account = service_account

    @property
    def service_account_name(self):
        """Gets the service_account_name of this V1PodSpec.  # noqa: E501

        ServiceAccountName is the name of the ServiceAccount to use to run this pod. More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/  # noqa: E501

        :return: The service_account_name of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._service_account_name

    @service_account_name.setter
    def service_account_name(self, service_account_name):
        """Sets the service_account_name of this V1PodSpec.

        ServiceAccountName is the name of the ServiceAccount to use to run this pod. More info: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/  # noqa: E501

        :param service_account_name: The service_account_name of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._service_account_name = service_account_name

    @property
    def set_hostname_as_fqdn(self):
        """Gets the set_hostname_as_fqdn of this V1PodSpec.  # noqa: E501

        If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default). In Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname). In Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters to FQDN. If a pod does not have FQDN, this has no effect. Default to false.  # noqa: E501

        :return: The set_hostname_as_fqdn of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._set_hostname_as_fqdn

    @set_hostname_as_fqdn.setter
    def set_hostname_as_fqdn(self, set_hostname_as_fqdn):
        """Sets the set_hostname_as_fqdn of this V1PodSpec.

        If true the pod's hostname will be configured as the pod's FQDN, rather than the leaf name (the default). In Linux containers, this means setting the FQDN in the hostname field of the kernel (the nodename field of struct utsname). In Windows containers, this means setting the registry value of hostname for the registry key HKEY_LOCAL_MACHINE\\\\SYSTEM\\\\CurrentControlSet\\\\Services\\\\Tcpip\\\\Parameters to FQDN. If a pod does not have FQDN, this has no effect. Default to false.  # noqa: E501

        :param set_hostname_as_fqdn: The set_hostname_as_fqdn of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._set_hostname_as_fqdn = set_hostname_as_fqdn

    @property
    def share_process_namespace(self):
        """Gets the share_process_namespace of this V1PodSpec.  # noqa: E501

        Share a single process namespace between all of the containers in a pod. When this is set containers will be able to view and signal processes from other containers in the same pod, and the first process in each container will not be assigned PID 1. HostPID and ShareProcessNamespace cannot both be set. Optional: Default to false.  # noqa: E501

        :return: The share_process_namespace of this V1PodSpec.  # noqa: E501
        :rtype: bool
        """
        return self._share_process_namespace

    @share_process_namespace.setter
    def share_process_namespace(self, share_process_namespace):
        """Sets the share_process_namespace of this V1PodSpec.

        Share a single process namespace between all of the containers in a pod. When this is set containers will be able to view and signal processes from other containers in the same pod, and the first process in each container will not be assigned PID 1. HostPID and ShareProcessNamespace cannot both be set. Optional: Default to false.  # noqa: E501

        :param share_process_namespace: The share_process_namespace of this V1PodSpec.  # noqa: E501
        :type: bool
        """

        self._share_process_namespace = share_process_namespace

    @property
    def subdomain(self):
        """Gets the subdomain of this V1PodSpec.  # noqa: E501

        If specified, the fully qualified Pod hostname will be \"<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>\". If not specified, the pod will not have a domainname at all.  # noqa: E501

        :return: The subdomain of this V1PodSpec.  # noqa: E501
        :rtype: str
        """
        return self._subdomain

    @subdomain.setter
    def subdomain(self, subdomain):
        """Sets the subdomain of this V1PodSpec.

        If specified, the fully qualified Pod hostname will be \"<hostname>.<subdomain>.<pod namespace>.svc.<cluster domain>\". If not specified, the pod will not have a domainname at all.  # noqa: E501

        :param subdomain: The subdomain of this V1PodSpec.  # noqa: E501
        :type: str
        """

        self._subdomain = subdomain

    @property
    def termination_grace_period_seconds(self):
        """Gets the termination_grace_period_seconds of this V1PodSpec.  # noqa: E501

        Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). If this value is nil, the default grace period will be used instead. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. Defaults to 30 seconds.  # noqa: E501

        :return: The termination_grace_period_seconds of this V1PodSpec.  # noqa: E501
        :rtype: int
        """
        return self._termination_grace_period_seconds

    @termination_grace_period_seconds.setter
    def termination_grace_period_seconds(self, termination_grace_period_seconds):
        """Sets the termination_grace_period_seconds of this V1PodSpec.

        Optional duration in seconds the pod needs to terminate gracefully. May be decreased in delete request. Value must be non-negative integer. The value zero indicates stop immediately via the kill signal (no opportunity to shut down). If this value is nil, the default grace period will be used instead. The grace period is the duration in seconds after the processes running in the pod are sent a termination signal and the time when the processes are forcibly halted with a kill signal. Set this value longer than the expected cleanup time for your process. Defaults to 30 seconds.  # noqa: E501

        :param termination_grace_period_seconds: The termination_grace_period_seconds of this V1PodSpec.  # noqa: E501
        :type: int
        """

        self._termination_grace_period_seconds = termination_grace_period_seconds

    @property
    def tolerations(self):
        """Gets the tolerations of this V1PodSpec.  # noqa: E501

        If specified, the pod's tolerations.  # noqa: E501

        :return: The tolerations of this V1PodSpec.  # noqa: E501
        :rtype: list[V1Toleration]
        """
        return self._tolerations

    @tolerations.setter
    def tolerations(self, tolerations):
        """Sets the tolerations of this V1PodSpec.

        If specified, the pod's tolerations.  # noqa: E501

        :param tolerations: The tolerations of this V1PodSpec.  # noqa: E501
        :type: list[V1Toleration]
        """

        self._tolerations = tolerations

    @property
    def topology_spread_constraints(self):
        """Gets the topology_spread_constraints of this V1PodSpec.  # noqa: E501

        TopologySpreadConstraints describes how a group of pods ought to spread across topology domains. Scheduler will schedule pods in a way which abides by the constraints. All topologySpreadConstraints are ANDed.  # noqa: E501

        :return: The topology_spread_constraints of this V1PodSpec.  # noqa: E501
        :rtype: list[V1TopologySpreadConstraint]
        """
        return self._topology_spread_constraints

    @topology_spread_constraints.setter
    def topology_spread_constraints(self, topology_spread_constraints):
        """Sets the topology_spread_constraints of this V1PodSpec.

        TopologySpreadConstraints describes how a group of pods ought to spread across topology domains. Scheduler will schedule pods in a way which abides by the constraints. All topologySpreadConstraints are ANDed.  # noqa: E501

        :param topology_spread_constraints: The topology_spread_constraints of this V1PodSpec.  # noqa: E501
        :type: list[V1TopologySpreadConstraint]
        """

        self._topology_spread_constraints = topology_spread_constraints

    @property
    def volumes(self):
        """Gets the volumes of this V1PodSpec.  # noqa: E501

        List of volumes that can be mounted by containers belonging to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes  # noqa: E501

        :return: The volumes of this V1PodSpec.  # noqa: E501
        :rtype: list[V1Volume]
        """
        return self._volumes

    @volumes.setter
    def volumes(self, volumes):
        """Sets the volumes of this V1PodSpec.

        List of volumes that can be mounted by containers belonging to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes  # noqa: E501

        :param volumes: The volumes of this V1PodSpec.  # noqa: E501
        :type: list[V1Volume]
        """

        self._volumes = volumes

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1PodSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1PodSpec):
            return True

        return self.to_dict() != other.to_dict()
