# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1ServiceAccount(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'api_version': 'str',
        'automount_service_account_token': 'bool',
        'image_pull_secrets': 'list[V1LocalObjectReference]',
        'kind': 'str',
        'metadata': 'V1ObjectMeta',
        'secrets': 'list[V1ObjectReference]'
    }

    attribute_map = {
        'api_version': 'apiVersion',
        'automount_service_account_token': 'automountServiceAccountToken',
        'image_pull_secrets': 'imagePullSecrets',
        'kind': 'kind',
        'metadata': 'metadata',
        'secrets': 'secrets'
    }

    def __init__(self, api_version=None, automount_service_account_token=None, image_pull_secrets=None, kind=None, metadata=None, secrets=None, local_vars_configuration=None):  # noqa: E501
        """V1ServiceAccount - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._api_version = None
        self._automount_service_account_token = None
        self._image_pull_secrets = None
        self._kind = None
        self._metadata = None
        self._secrets = None
        self.discriminator = None

        if api_version is not None:
            self.api_version = api_version
        if automount_service_account_token is not None:
            self.automount_service_account_token = automount_service_account_token
        if image_pull_secrets is not None:
            self.image_pull_secrets = image_pull_secrets
        if kind is not None:
            self.kind = kind
        if metadata is not None:
            self.metadata = metadata
        if secrets is not None:
            self.secrets = secrets

    @property
    def api_version(self):
        """Gets the api_version of this V1ServiceAccount.  # noqa: E501

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :return: The api_version of this V1ServiceAccount.  # noqa: E501
        :rtype: str
        """
        return self._api_version

    @api_version.setter
    def api_version(self, api_version):
        """Sets the api_version of this V1ServiceAccount.

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :param api_version: The api_version of this V1ServiceAccount.  # noqa: E501
        :type: str
        """

        self._api_version = api_version

    @property
    def automount_service_account_token(self):
        """Gets the automount_service_account_token of this V1ServiceAccount.  # noqa: E501

        AutomountServiceAccountToken indicates whether pods running as this service account should have an API token automatically mounted. Can be overridden at the pod level.  # noqa: E501

        :return: The automount_service_account_token of this V1ServiceAccount.  # noqa: E501
        :rtype: bool
        """
        return self._automount_service_account_token

    @automount_service_account_token.setter
    def automount_service_account_token(self, automount_service_account_token):
        """Sets the automount_service_account_token of this V1ServiceAccount.

        AutomountServiceAccountToken indicates whether pods running as this service account should have an API token automatically mounted. Can be overridden at the pod level.  # noqa: E501

        :param automount_service_account_token: The automount_service_account_token of this V1ServiceAccount.  # noqa: E501
        :type: bool
        """

        self._automount_service_account_token = automount_service_account_token

    @property
    def image_pull_secrets(self):
        """Gets the image_pull_secrets of this V1ServiceAccount.  # noqa: E501

        ImagePullSecrets is a list of references to secrets in the same namespace to use for pulling any images in pods that reference this ServiceAccount. ImagePullSecrets are distinct from Secrets because Secrets can be mounted in the pod, but ImagePullSecrets are only accessed by the kubelet. More info: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod  # noqa: E501

        :return: The image_pull_secrets of this V1ServiceAccount.  # noqa: E501
        :rtype: list[V1LocalObjectReference]
        """
        return self._image_pull_secrets

    @image_pull_secrets.setter
    def image_pull_secrets(self, image_pull_secrets):
        """Sets the image_pull_secrets of this V1ServiceAccount.

        ImagePullSecrets is a list of references to secrets in the same namespace to use for pulling any images in pods that reference this ServiceAccount. ImagePullSecrets are distinct from Secrets because Secrets can be mounted in the pod, but ImagePullSecrets are only accessed by the kubelet. More info: https://kubernetes.io/docs/concepts/containers/images/#specifying-imagepullsecrets-on-a-pod  # noqa: E501

        :param image_pull_secrets: The image_pull_secrets of this V1ServiceAccount.  # noqa: E501
        :type: list[V1LocalObjectReference]
        """

        self._image_pull_secrets = image_pull_secrets

    @property
    def kind(self):
        """Gets the kind of this V1ServiceAccount.  # noqa: E501

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :return: The kind of this V1ServiceAccount.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this V1ServiceAccount.

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :param kind: The kind of this V1ServiceAccount.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def metadata(self):
        """Gets the metadata of this V1ServiceAccount.  # noqa: E501


        :return: The metadata of this V1ServiceAccount.  # noqa: E501
        :rtype: V1ObjectMeta
        """
        return self._metadata

    @metadata.setter
    def metadata(self, metadata):
        """Sets the metadata of this V1ServiceAccount.


        :param metadata: The metadata of this V1ServiceAccount.  # noqa: E501
        :type: V1ObjectMeta
        """

        self._metadata = metadata

    @property
    def secrets(self):
        """Gets the secrets of this V1ServiceAccount.  # noqa: E501

        Secrets is a list of the secrets in the same namespace that pods running using this ServiceAccount are allowed to use. Pods are only limited to this list if this service account has a \"kubernetes.io/enforce-mountable-secrets\" annotation set to \"true\". The \"kubernetes.io/enforce-mountable-secrets\" annotation is deprecated since v1.32. Prefer separate namespaces to isolate access to mounted secrets. This field should not be used to find auto-generated service account token secrets for use outside of pods. Instead, tokens can be requested directly using the TokenRequest API, or service account token secrets can be manually created. More info: https://kubernetes.io/docs/concepts/configuration/secret  # noqa: E501

        :return: The secrets of this V1ServiceAccount.  # noqa: E501
        :rtype: list[V1ObjectReference]
        """
        return self._secrets

    @secrets.setter
    def secrets(self, secrets):
        """Sets the secrets of this V1ServiceAccount.

        Secrets is a list of the secrets in the same namespace that pods running using this ServiceAccount are allowed to use. Pods are only limited to this list if this service account has a \"kubernetes.io/enforce-mountable-secrets\" annotation set to \"true\". The \"kubernetes.io/enforce-mountable-secrets\" annotation is deprecated since v1.32. Prefer separate namespaces to isolate access to mounted secrets. This field should not be used to find auto-generated service account token secrets for use outside of pods. Instead, tokens can be requested directly using the TokenRequest API, or service account token secrets can be manually created. More info: https://kubernetes.io/docs/concepts/configuration/secret  # noqa: E501

        :param secrets: The secrets of this V1ServiceAccount.  # noqa: E501
        :type: list[V1ObjectReference]
        """

        self._secrets = secrets

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1ServiceAccount):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1ServiceAccount):
            return True

        return self.to_dict() != other.to_dict()
