# A.T.L.A.S Trading System - Testing Environment
# Optimized for automated testing and CI/CD pipelines

# =============================================================================
# TESTING CONFIGURATION - Safe for automated testing
# =============================================================================

# Alpaca Trading API (Paper Trading Only)
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=test_alpaca_api_key_placeholder
ALPACA_SECRET_KEY=test_alpaca_secret_key_placeholder

# Financial Modeling Prep API (Optional for testing)
FMP_API_KEY=test_fmp_api_key_placeholder

# OpenAI API (Optional for testing)
OPENAI_API_KEY=test_openai_api_key_placeholder

# Predicto API (Optional for testing)
PREDICTO_API_KEY=test_predicto_api_key_placeholder

# =============================================================================
# TESTING MODE CONFIGURATION
# =============================================================================

# Enable validation mode for testing without real API keys
VALIDATION_MODE=true

# Safe testing environment
ENVIRONMENT=testing
PAPER_TRADING=true
DEBUG=true

# Testing-specific settings
LOG_LEVEL=DEBUG
PORT=8080

# Test database (in-memory or temporary)
DATABASE_URL=sqlite:///:memory:

# Performance settings for testing
API_TIMEOUT=10
CACHE_TTL=60
MAX_SCAN_RESULTS=10

# ML Model Configuration (disabled for faster testing)
ML_MODELS_ENABLED=false
ML_PREDICTION_CONFIDENCE_THRESHOLD=0.5

# Options Trading Configuration (minimal for testing)
OPTIONS_TRADING_ENABLED=true
OPTIONS_MAX_EXPIRY_DAYS=30
OPTIONS_MIN_VOLUME=50
OPTIONS_MAX_SPREAD_PERCENT=10.0

# Risk Management (conservative for testing)
DEFAULT_RISK_PERCENT=1.0
MAX_POSITIONS=5

# Proactive Assistant Configuration (minimal for testing)
PROACTIVE_ASSISTANT_ENABLED=false
MORNING_BRIEFING_TIME=09:00
ALERT_COOLDOWN_MINUTES=30
MIN_SIGNAL_STRENGTH=3

# Enhanced Memory System (disabled for testing)
ENHANCED_MEMORY_ENABLED=false
CONVERSATION_MEMORY_LIMIT=100
MEMORY_IMPORTANCE_THRESHOLD=0.7

# =============================================================================
# CI/CD TESTING CONFIGURATION
# =============================================================================

# Disable external API calls for unit testing
EXTERNAL_API_CALLS_ENABLED=false

# Fast testing mode
FAST_TESTING_MODE=true

# Disable real-time features for testing
REALTIME_FEATURES_ENABLED=false

# Test-specific timeouts
TEST_TIMEOUT=30
HTTP_TIMEOUT=5

# Disable logging to files during testing
LOG_TO_FILE=false
LOG_TO_CONSOLE=true
