# A.T.L.A.S. Trading System - Git Ignore File

# =============================================================================
# CRITICAL SECURITY: Environment files with API keys
# =============================================================================
.env
.env.local
.env.production
.env.staging
.env.test
*.env

# Exception: Allow example and template files
!.env.example
!.env.template

# =============================================================================
# Python
# =============================================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# A.T.L.A.S. Specific Files
# =============================================================================

# Database files
*.db
*.sqlite
*.sqlite3
atlas_*.db

# Log files
*.log
logs/
atlas.log

# Cache files
cache/
*.cache
.cache/

# Model files (large ML models)
models/*.h5
models/*.pkl
models/*.joblib
*.model

# Data files
data/
*.csv
*.json
historical_data/

# Temporary files
temp/
tmp/
*.tmp

# Configuration backups
config_backup/
*.bak

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# Security & Compliance
# =============================================================================

# API keys and secrets
secrets/
keys/
certificates/
*.pem
*.key
*.crt

# Compliance reports (may contain sensitive data)
compliance_reports/
audit_logs/
security_reports/

# User data (PII protection)
user_data/
kyc_documents/
encrypted_data/

# =============================================================================
# Development & Testing
# =============================================================================

# Test outputs
test_reports/
test_results/
coverage_reports/

# Development databases
dev_*.db
test_*.db

# Backup files
*.backup
backups/

# Performance profiling
*.prof
profiling_results/

# =============================================================================
# Docker & Deployment
# =============================================================================

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes secrets
k8s/secrets/
*.secret.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# =============================================================================
# Documentation
# =============================================================================

# Generated documentation
docs/build/
docs/_build/

# =============================================================================
# Node.js (if using any frontend components)
# =============================================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
