2025-07-13 16:25:24 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:25:24 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:25:24 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:25:24 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:25:24 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:33:01 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:33:01 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:33:01 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:33:01 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:33:01 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:34:23 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:34:23 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:34:23 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:34:23 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:34:23 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 16:43:05 [INFO] atlas_server:78 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 16:43:05 [INFO] atlas_server:79 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 16:43:05 [INFO] atlas_server:80 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 16:43:05 [INFO] atlas_server:125 - initialize_system(): Starting background initialization...
2025-07-13 16:43:05 [INFO] atlas_server:137 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:02:08 [INFO] atlas_error_handler:94 - __init__(): [SHIELD] Enhanced Error Handler initialized
2025-07-13 17:02:08 [INFO] atlas_performance_optimizer:153 - __init__(): [LAUNCH] Performance Optimizer initialized
2025-07-13 17:02:08 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:02:08 [INFO] atlas_proactive_assistant:308 - __init__(): [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 17:02:08 [INFO] atlas_logging:54 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 17:02:08 [INFO] atlas_logging:55 - setup_atlas_logging(): Log level: INFO
2025-07-13 17:02:08 [INFO] atlas_logging:56 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 17:02:08 [INFO] __main__:79 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 17:02:08 [INFO] __main__:80 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 17:02:08 [INFO] __main__:81 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 17:02:08 [INFO] __main__:126 - initialize_system(): Starting background initialization...
2025-07-13 17:02:08 [INFO] __main__:138 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:02:08 [INFO] atlas_orchestrator:74 - initialize_component_logging(): [INIT] atlas_orchestrator component logging initialized
2025-07-13 17:02:08 [INFO] __main__:142 - initialize_system(): Initializing AtlasOrchestrator...
2025-07-13 17:02:08 [WARNING] atlas_orchestrator:109 - __init__(): [WARN] Error handler setup failed: name 'setup_component_recovery_strategies' is not defined
2025-07-13 17:02:08 [INFO] atlas_orchestrator:114 - __init__(): AtlasOrchestrator created - components will load on demand
2025-07-13 17:02:08 [INFO] atlas_orchestrator:131 - initialize_with_progress(): Starting comprehensive system initialization...
2025-07-13 17:02:08 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:02:08 [ERROR] atlas_orchestrator:193 - _ensure_database_manager(): Database manager initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:02:08 [ERROR] atlas_orchestrator:178 - initialize_with_progress(): System initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:02:08 [ERROR] __main__:159 - initialize_system(): System initialization failed: 'AtlasSecurityManager' object has no attribute 'initialize'
2025-07-13 17:06:42 [INFO] atlas_error_handler:94 - __init__(): [SHIELD] Enhanced Error Handler initialized
2025-07-13 17:06:42 [INFO] atlas_performance_optimizer:153 - __init__(): [LAUNCH] Performance Optimizer initialized
2025-07-13 17:06:42 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:06:42 [INFO] atlas_proactive_assistant:308 - __init__(): [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 17:06:42 [INFO] atlas_logging:54 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 17:06:42 [INFO] atlas_logging:55 - setup_atlas_logging(): Log level: INFO
2025-07-13 17:06:42 [INFO] atlas_logging:56 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 17:06:42 [INFO] __main__:79 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 17:06:42 [INFO] __main__:80 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 17:06:42 [INFO] __main__:81 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 17:06:42 [INFO] __main__:126 - initialize_system(): Starting background initialization...
2025-07-13 17:06:42 [INFO] __main__:138 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:06:42 [INFO] atlas_orchestrator:74 - initialize_component_logging(): [INIT] atlas_orchestrator component logging initialized
2025-07-13 17:06:42 [INFO] __main__:142 - initialize_system(): Initializing AtlasOrchestrator...
2025-07-13 17:06:42 [WARNING] atlas_orchestrator:109 - __init__(): [WARN] Error handler setup failed: name 'setup_component_recovery_strategies' is not defined
2025-07-13 17:06:42 [INFO] atlas_orchestrator:114 - __init__(): AtlasOrchestrator created - components will load on demand
2025-07-13 17:06:42 [INFO] atlas_orchestrator:131 - initialize_with_progress(): Starting comprehensive system initialization...
2025-07-13 17:06:42 [INFO] atlas_database:42 - __init__(): Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Main database schema created: atlas.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'main' initialized: atlas.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Memory database schema created: atlas_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'memory' initialized: atlas_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Rag database schema created: atlas_rag.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'rag' initialized: atlas_rag.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Compliance database schema created: atlas_compliance.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'compliance' initialized: atlas_compliance.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Feedback database schema created: atlas_feedback.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'feedback' initialized: atlas_feedback.db
2025-07-13 17:06:42 [INFO] atlas_database:180 - _create_schema(): Enhanced_Memory database schema created: atlas_enhanced_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:79 - _initialize_database(): Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 17:06:42 [INFO] atlas_database:58 - initialize(): All 6 databases initialized successfully
2025-07-13 17:06:42 [INFO] atlas_orchestrator:191 - _ensure_database_manager(): Database manager initialized
2025-07-13 17:06:48 [INFO] atlas_ai_core:88 - __init__(): [CHAT] Conversation Flow Manager created
2025-07-13 17:06:48 [INFO] atlas_ai_core:153 - __init__(): [WEB] Unified System Access Layer created
2025-07-13 17:06:48 [INFO] atlas_ai_core:236 - __init__(): Predicto Conversational Engine created
2025-07-13 17:06:48 [INFO] atlas_ai_core:88 - __init__(): [CHAT] Conversation Flow Manager created
2025-07-13 17:06:48 [INFO] atlas_ai_core:153 - __init__(): [WEB] Unified System Access Layer created
2025-07-13 17:06:48 [INFO] atlas_ai_core:523 - __init__(): Atlas AI Engine created with Predicto integration
2025-07-13 17:06:48 [INFO] atlas_ai_core:247 - initialize(): [OK] OpenAI client initialized
2025-07-13 17:06:48 [INFO] atlas_ai_core:254 - initialize(): [OK] Predicto Conversational Engine fully initialized
2025-07-13 17:06:48 [INFO] atlas_ai_core:534 - initialize(): [OK] Atlas AI Engine fully initialized
2025-07-13 17:06:48 [INFO] atlas_orchestrator:216 - _ensure_ai_engine(): AI engine initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:298 - __init__(): [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 17:06:48 [INFO] atlas_market_core:445 - __init__(): [BRAIN] Stock Intelligence Hub created
2025-07-13 17:06:48 [INFO] atlas_market_core:72 - __init__(): [DATA] Market Engine created - API clients will load on demand
2025-07-13 17:06:48 [INFO] atlas_trading_core:55 - __init__(): [GOD] Trading God Engine created
2025-07-13 17:06:48 [INFO] atlas_trading_core:211 - __init__(): [AUTO] Auto Trading Engine created
2025-07-13 17:06:48 [INFO] atlas_trading_core:296 - __init__(): Atlas Trading Engine created
2025-07-13 17:06:48 [INFO] atlas_trading_core:315 - initialize(): [OK] Paper trading mode enabled
2025-07-13 17:06:48 [INFO] atlas_trading_core:318 - initialize(): [OK] Trading Engine initialization completed
2025-07-13 17:06:48 [INFO] atlas_orchestrator:259 - _ensure_trading_engine(): Trading engine initialized
2025-07-13 17:06:48 [INFO] atlas_risk_core:46 - __init__(): [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 17:06:48 [INFO] atlas_risk_core:80 - _load_risk_parameters(): [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 17:06:48 [INFO] atlas_risk_core:97 - _initialize_monitoring(): [SEARCH] Risk monitoring systems initialized
2025-07-13 17:06:48 [INFO] atlas_risk_core:60 - initialize(): [OK] Risk Engine initialization completed
2025-07-13 17:06:48 [INFO] atlas_orchestrator:280 - _ensure_risk_engine(): Risk engine initialized
2025-07-13 17:06:48 [INFO] atlas_education:43 - __init__(): [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 17:06:48 [INFO] atlas_education:162 - _load_educational_content(): [BOOK] Basic educational content loaded
2025-07-13 17:06:48 [INFO] atlas_education:204 - _initialize_beginner_mentor(): [MENTOR] Beginner trading mentor initialized
2025-07-13 17:06:48 [INFO] atlas_education:57 - initialize(): [OK] Education Engine initialization completed
2025-07-13 17:06:48 [INFO] atlas_orchestrator:301 - _ensure_education_engine(): Education engine initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:107 - _test_connections(): [OK] FMP API connection tested successfully
2025-07-13 17:06:48 [INFO] atlas_market_core:116 - _test_connections(): [OK] Predicto API configured
2025-07-13 17:06:48 [INFO] atlas_market_core:304 - initialize(): [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 17:06:48 [INFO] atlas_market_core:528 - initialize(): [OK] Technical Analysis Module initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:546 - initialize(): [OK] Sentiment Analysis Module initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:564 - initialize(): [OK] Prediction Engine Module initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:582 - initialize(): [OK] Market Intelligence Module initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:459 - initialize(): [OK] Stock Intelligence Hub fully initialized
2025-07-13 17:06:48 [INFO] atlas_market_core:90 - initialize(): [OK] Market Engine initialization completed
2025-07-13 17:06:48 [INFO] atlas_orchestrator:238 - _ensure_market_engine(): Market engine initialized
2025-07-13 17:06:48 [INFO] atlas_orchestrator:175 - initialize_with_progress(): System initialization completed
2025-07-13 17:06:48 [INFO] __main__:156 - initialize_system(): A.T.L.A.S system initialization completed successfully
2025-07-13 17:21:15 [INFO] __main__:285 - predicto_chat_endpoint(): Predicto processing: Hello, testing the consolidated system...
2025-07-13 17:21:15 [INFO] atlas_orchestrator:572 - _ensure_trading_god_engine(): Trading God Engine initialized
2025-07-13 17:21:15 [INFO] atlas_orchestrator:621 - _is_sophisticated_guru_response(): [SHIELD] Protecting conversational response type 'greeting' from Trading God transformation
2025-07-13 17:21:23 [INFO] __main__:285 - predicto_chat_endpoint(): Predicto processing: Should I buy AAPL?...
2025-07-13 17:21:24 [ERROR] atlas_orchestrator:558 - process_message(): Error processing message: float division by zero
2025-07-13 17:22:10 [INFO] atlas_error_handler:94 - __init__(): [SHIELD] Enhanced Error Handler initialized
2025-07-13 17:22:10 [INFO] atlas_performance_optimizer:153 - __init__(): [LAUNCH] Performance Optimizer initialized
2025-07-13 17:22:10 [INFO] atlas_security:249 - __init__(): [SHIELD] Security Manager initialized
2025-07-13 17:22:10 [INFO] atlas_proactive_assistant:308 - __init__(): [BOT] Proactive Trading Assistant initialized - enabled: True
2025-07-13 17:22:10 [INFO] atlas_logging:54 - setup_atlas_logging(): A.T.L.A.S. logging system initialized - Windows compatible format
2025-07-13 17:22:10 [INFO] atlas_logging:55 - setup_atlas_logging(): Log level: INFO
2025-07-13 17:22:10 [INFO] atlas_logging:56 - setup_atlas_logging(): Unicode characters will be automatically converted to ASCII
2025-07-13 17:22:10 [INFO] __main__:79 - lifespan(): Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture
2025-07-13 17:22:10 [INFO] __main__:80 - lifespan(): Advanced Trading & Learning Analysis System v4.0
2025-07-13 17:22:10 [INFO] __main__:81 - lifespan(): Server starting immediately, background initialization in progress...
2025-07-13 17:22:10 [INFO] __main__:126 - initialize_system(): Starting background initialization...
2025-07-13 17:22:10 [INFO] __main__:138 - initialize_system(): Importing AtlasOrchestrator...
2025-07-13 17:22:10 [INFO] atlas_orchestrator:74 - initialize_component_logging(): [INIT] atlas_orchestrator component logging initialized
2025-07-13 17:22:10 [INFO] __main__:142 - initialize_system(): Initializing AtlasOrchestrator...
2025-07-13 17:22:10 [WARNING] atlas_orchestrator:109 - __init__(): [WARN] Error handler setup failed: name 'setup_component_recovery_strategies' is not defined
2025-07-13 17:22:10 [INFO] atlas_orchestrator:114 - __init__(): AtlasOrchestrator created - components will load on demand
2025-07-13 17:22:10 [INFO] atlas_orchestrator:131 - initialize_with_progress(): Starting comprehensive system initialization...
2025-07-13 17:22:10 [INFO] atlas_database:42 - __init__(): Database manager created - main: atlas.db, enhanced: 6 databases
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Main database schema created: atlas.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'main' initialized: atlas.db
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Memory database schema created: atlas_memory.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'memory' initialized: atlas_memory.db
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Rag database schema created: atlas_rag.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'rag' initialized: atlas_rag.db
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Compliance database schema created: atlas_compliance.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'compliance' initialized: atlas_compliance.db
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Feedback database schema created: atlas_feedback.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'feedback' initialized: atlas_feedback.db
2025-07-13 17:22:10 [INFO] atlas_database:180 - _create_schema(): Enhanced_Memory database schema created: atlas_enhanced_memory.db
2025-07-13 17:22:10 [INFO] atlas_database:79 - _initialize_database(): Database 'enhanced_memory' initialized: atlas_enhanced_memory.db
2025-07-13 17:22:10 [INFO] atlas_database:58 - initialize(): All 6 databases initialized successfully
2025-07-13 17:22:10 [INFO] atlas_orchestrator:191 - _ensure_database_manager(): Database manager initialized
2025-07-13 17:22:14 [INFO] atlas_ai_core:88 - __init__(): [CHAT] Conversation Flow Manager created
2025-07-13 17:22:14 [INFO] atlas_ai_core:153 - __init__(): [WEB] Unified System Access Layer created
2025-07-13 17:22:14 [INFO] atlas_ai_core:236 - __init__(): Predicto Conversational Engine created
2025-07-13 17:22:14 [INFO] atlas_ai_core:88 - __init__(): [CHAT] Conversation Flow Manager created
2025-07-13 17:22:14 [INFO] atlas_ai_core:153 - __init__(): [WEB] Unified System Access Layer created
2025-07-13 17:22:14 [INFO] atlas_ai_core:523 - __init__(): Atlas AI Engine created with Predicto integration
2025-07-13 17:22:14 [INFO] atlas_ai_core:247 - initialize(): [OK] OpenAI client initialized
2025-07-13 17:22:14 [INFO] atlas_ai_core:254 - initialize(): [OK] Predicto Conversational Engine fully initialized
2025-07-13 17:22:14 [INFO] atlas_ai_core:534 - initialize(): [OK] Atlas AI Engine fully initialized
2025-07-13 17:22:14 [INFO] atlas_orchestrator:216 - _ensure_ai_engine(): AI engine initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:298 - __init__(): [OK] Enhanced Scanner Suite initialized with 20+ scanners
2025-07-13 17:22:15 [INFO] atlas_market_core:445 - __init__(): [BRAIN] Stock Intelligence Hub created
2025-07-13 17:22:15 [INFO] atlas_market_core:72 - __init__(): [DATA] Market Engine created - API clients will load on demand
2025-07-13 17:22:15 [INFO] atlas_trading_core:55 - __init__(): [GOD] Trading God Engine created
2025-07-13 17:22:15 [INFO] atlas_trading_core:211 - __init__(): [AUTO] Auto Trading Engine created
2025-07-13 17:22:15 [INFO] atlas_trading_core:296 - __init__(): Atlas Trading Engine created
2025-07-13 17:22:15 [INFO] atlas_trading_core:315 - initialize(): [OK] Paper trading mode enabled
2025-07-13 17:22:15 [INFO] atlas_trading_core:318 - initialize(): [OK] Trading Engine initialization completed
2025-07-13 17:22:15 [INFO] atlas_orchestrator:259 - _ensure_trading_engine(): Trading engine initialized
2025-07-13 17:22:15 [INFO] atlas_risk_core:46 - __init__(): [SHIELD] Risk Engine created - safety guardrails active
2025-07-13 17:22:15 [INFO] atlas_risk_core:80 - _load_risk_parameters(): [DATA] Risk parameters loaded - Max position size: 2.0%
2025-07-13 17:22:15 [INFO] atlas_risk_core:97 - _initialize_monitoring(): [SEARCH] Risk monitoring systems initialized
2025-07-13 17:22:15 [INFO] atlas_risk_core:60 - initialize(): [OK] Risk Engine initialization completed
2025-07-13 17:22:15 [INFO] atlas_orchestrator:280 - _ensure_risk_engine(): Risk engine initialized
2025-07-13 17:22:15 [INFO] atlas_education:43 - __init__(): [LIBRARY] Education Engine created - ChromaDB will load on demand
2025-07-13 17:22:15 [INFO] atlas_education:162 - _load_educational_content(): [BOOK] Basic educational content loaded
2025-07-13 17:22:15 [INFO] atlas_education:204 - _initialize_beginner_mentor(): [MENTOR] Beginner trading mentor initialized
2025-07-13 17:22:15 [INFO] atlas_education:57 - initialize(): [OK] Education Engine initialization completed
2025-07-13 17:22:15 [INFO] atlas_orchestrator:301 - _ensure_education_engine(): Education engine initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:107 - _test_connections(): [OK] FMP API connection tested successfully
2025-07-13 17:22:15 [INFO] atlas_market_core:116 - _test_connections(): [OK] Predicto API configured
2025-07-13 17:22:15 [INFO] atlas_market_core:304 - initialize(): [OK] Enhanced Scanner Suite connected to market engine
2025-07-13 17:22:15 [INFO] atlas_market_core:528 - initialize(): [OK] Technical Analysis Module initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:546 - initialize(): [OK] Sentiment Analysis Module initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:564 - initialize(): [OK] Prediction Engine Module initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:582 - initialize(): [OK] Market Intelligence Module initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:459 - initialize(): [OK] Stock Intelligence Hub fully initialized
2025-07-13 17:22:15 [INFO] atlas_market_core:90 - initialize(): [OK] Market Engine initialization completed
2025-07-13 17:22:15 [INFO] atlas_orchestrator:238 - _ensure_market_engine(): Market engine initialized
2025-07-13 17:22:15 [INFO] atlas_orchestrator:175 - initialize_with_progress(): System initialization completed
2025-07-13 17:22:15 [INFO] __main__:156 - initialize_system(): A.T.L.A.S system initialization completed successfully
2025-07-13 17:22:34 [INFO] __main__:285 - predicto_chat_endpoint(): Predicto processing: Should I buy AAPL?...
2025-07-13 17:22:34 [INFO] atlas_orchestrator:572 - _ensure_trading_god_engine(): Trading God Engine initialized
2025-07-13 17:22:34 [ERROR] atlas_orchestrator:558 - process_message(): Error processing message: float division by zero
