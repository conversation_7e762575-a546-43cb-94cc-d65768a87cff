
import asyncio
import threading
from typing import Optional, Any
import logging

logger = logging.getLogger(__name__)

class AsyncSafeScanner:
    """Async-safe wrapper for scanner operations"""
    
    def __init__(self):
        self._thread_local = threading.local()
    
    def get_or_create_loop(self) -> asyncio.AbstractEventLoop:
        """Get or create an event loop for the current thread"""
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we need a new one for this thread
                if not hasattr(self._thread_local, 'loop'):
                    self._thread_local.loop = asyncio.new_event_loop()
                return self._thread_local.loop
            return loop
        except RuntimeError:
            # No event loop in this thread, create one
            if not hasattr(self._thread_local, 'loop'):
                self._thread_local.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._thread_local.loop)
            return self._thread_local.loop
    
    def run_async_safe(self, coro) -> Optional[Any]:
        """Run an async coroutine safely in any thread context"""
        try:
            loop = self.get_or_create_loop()
            
            if loop.is_running():
                # Loop is running, we can't use run_until_complete
                # Create a future and run it in a thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, coro)
                    return future.result(timeout=10)
            else:
                # Loop is not running, safe to use run_until_complete
                return loop.run_until_complete(coro)
                
        except Exception as e:
            logger.error(f"Async-safe execution failed: {e}")
            return None
    
    def cleanup(self):
        """Cleanup thread-local event loops"""
        try:
            if hasattr(self._thread_local, 'loop'):
                loop = self._thread_local.loop
                if not loop.is_closed():
                    loop.close()
                delattr(self._thread_local, 'loop')
        except Exception as e:
            logger.error(f"Cleanup failed: {e}")

# Global async-safe scanner instance
async_safe_scanner = AsyncSafeScanner()
