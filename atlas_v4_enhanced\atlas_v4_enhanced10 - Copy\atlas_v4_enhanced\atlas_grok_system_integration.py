"""
A.T.L.A.S. Grok System Integration Module
Integrates advanced Grok capabilities with existing A.T.L.A.S. components:
- Lee Method Scanner enhancement
- TTM Squeeze pattern analysis
- AI Core engine integration
- Real-time scanner optimization
- Trading engine intelligence boost
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json

# Core A.T.L.A.S. imports
from atlas_lee_method import LeeMethodScanner, LeeMethodSignal
from atlas_realtime_scanner import AtlasRealtimeScanner
from atlas_ai_core import AtlasAIEngine, AtlasConversationalEngine
from atlas_trading_core import AtlasTradingEngine
from atlas_market_core import AtlasMarketEngine
from models import EngineStatus, TTMSqueezeSignal, SignalStrength

# Enhanced Grok integration imports
from atlas_grok_integration import (
    AtlasGrokIntegrationEngine, GrokRequest, GrokResponse,
    GrokTaskType, GrokCapability, TradingSignal, MarketAnalysis,
    MARKET_SEARCH_CONFIGS
)
from grok_performance_optimizer import OptimizedGrokClient
from grok_resilience_manager import ResilienceManager

logger = logging.getLogger(__name__)

class GrokEnhancedLeeMethodScanner(LeeMethodScanner):
    """Lee Method Scanner enhanced with Grok AI capabilities"""
    
    def __init__(self, fmp_api_key: str, market_engine: AtlasMarketEngine):
        super().__init__(fmp_api_key, market_engine)
        self.grok_engine = None
        self.grok_enhanced = False
        
    async def initialize_grok_enhancement(self):
        """Initialize Grok enhancement for Lee Method scanning"""
        try:
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if success:
                self.grok_enhanced = True
                logger.info("✅ Lee Method Scanner enhanced with Grok AI capabilities")
            else:
                logger.warning("⚠️ Grok enhancement unavailable - using standard Lee Method")
                
        except Exception as e:
            logger.error(f"Failed to initialize Grok enhancement: {e}")
    
    async def enhanced_pattern_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced pattern analysis using Grok reasoning"""
        if not self.grok_enhanced:
            return await self.standard_pattern_analysis(symbol, market_data)
        
        try:
            # Create comprehensive analysis prompt
            prompt = f"""
            Analyze this TTM Squeeze pattern for {symbol} using advanced market reasoning:
            
            MARKET DATA:
            - Current Price: ${market_data.get('current_price', 'N/A')}
            - Volume: {market_data.get('volume', 'N/A')}
            - TTM Histogram: {market_data.get('ttm_histogram', [])}
            - Bollinger Bands: {market_data.get('bollinger_bands', {})}
            - Keltner Channels: {market_data.get('keltner_channels', {})}
            
            ANALYSIS REQUIREMENTS:
            1. **Pattern Validation**: Confirm TTM Squeeze pattern authenticity
            2. **Momentum Assessment**: Analyze histogram momentum shifts
            3. **Breakout Probability**: Calculate likelihood of directional breakout
            4. **Risk/Reward Analysis**: Assess potential profit vs. risk
            5. **Entry Timing**: Determine optimal entry conditions
            6. **Market Context**: Consider broader market conditions
            
            Provide specific, actionable trading recommendations with confidence scores.
            """
            
            # Get enhanced reasoning analysis
            reasoning_result = await self.grok_engine.enhanced_market_reasoning(
                market_scenario=prompt,
                symbol=symbol,
                reasoning_effort="high"
            )
            
            if reasoning_result.grok_enhancement.success:
                return {
                    'enhanced_analysis': reasoning_result.grok_enhancement.content,
                    'confidence': reasoning_result.combined_confidence,
                    'reasoning_chain': reasoning_result.reasoning_chain,
                    'grok_enhanced': True,
                    'processing_time': reasoning_result.grok_enhancement.processing_time
                }
            else:
                logger.warning(f"Grok analysis failed for {symbol}, using standard analysis")
                return await self.standard_pattern_analysis(symbol, market_data)
                
        except Exception as e:
            logger.error(f"Enhanced pattern analysis failed for {symbol}: {e}")
            return await self.standard_pattern_analysis(symbol, market_data)
    
    async def standard_pattern_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Standard pattern analysis fallback"""
        return {
            'analysis': f"Standard TTM Squeeze analysis for {symbol}",
            'confidence': 0.7,
            'grok_enhanced': False,
            'processing_time': 0.1
        }
    
    async def enhanced_signal_generation(self, symbol: str, pattern_data: Dict[str, Any]) -> Optional[LeeMethodSignal]:
        """Generate enhanced signals with Grok intelligence"""
        try:
            # Get enhanced pattern analysis
            analysis = await self.enhanced_pattern_analysis(symbol, pattern_data)
            
            # Generate structured trading signal if Grok is available
            if self.grok_enhanced and analysis.get('grok_enhanced'):
                structured_signal = await self.grok_engine.grok_client.make_structured_analysis_request(
                    symbol=symbol,
                    analysis_type="trading_signal"
                )
                
                if structured_signal.success and structured_signal.structured_output:
                    signal_data = structured_signal.structured_output
                    
                    # Create enhanced Lee Method signal
                    return LeeMethodSignal(
                        symbol=symbol,
                        signal_strength=self._map_confidence_to_strength(signal_data.get('confidence', 0.7)),
                        entry_price=signal_data.get('entry_price', pattern_data.get('current_price', 0)),
                        target_price=signal_data.get('target_price', 0),
                        stop_loss=signal_data.get('stop_loss', 0),
                        confidence=signal_data.get('confidence', 0.7),
                        timeframe="1D",
                        timestamp=datetime.now(),
                        pattern_type="ttm_squeeze_enhanced",
                        grok_enhanced=True,
                        reasoning=signal_data.get('reasoning', ''),
                        risk_level=signal_data.get('risk_level', 'MEDIUM')
                    )
            
            # Fallback to standard signal generation
            return await self.generate_standard_signal(symbol, pattern_data)
            
        except Exception as e:
            logger.error(f"Enhanced signal generation failed for {symbol}: {e}")
            return await self.generate_standard_signal(symbol, pattern_data)
    
    def _map_confidence_to_strength(self, confidence: float) -> SignalStrength:
        """Map confidence score to signal strength"""
        if confidence >= 0.8:
            return SignalStrength.STRONG
        elif confidence >= 0.6:
            return SignalStrength.MEDIUM
        else:
            return SignalStrength.WEAK

class GrokEnhancedRealtimeScanner(AtlasRealtimeScanner):
    """Real-time scanner enhanced with Grok live search and reasoning"""
    
    def __init__(self):
        super().__init__()
        self.grok_engine = None
        self.resilience_manager = ResilienceManager()
        self.live_search_enabled = False
        
    async def initialize_grok_enhancement(self):
        """Initialize Grok enhancement for real-time scanning"""
        try:
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if success:
                self.live_search_enabled = True
                logger.info("✅ Real-time Scanner enhanced with Grok live search")
            else:
                logger.warning("⚠️ Grok live search unavailable")
                
        except Exception as e:
            logger.error(f"Failed to initialize Grok enhancement: {e}")
    
    async def enhanced_market_context_analysis(self, symbols: List[str]) -> Dict[str, Any]:
        """Get enhanced market context using Grok live search"""
        if not self.live_search_enabled:
            return {"context": "Standard market analysis", "enhanced": False}
        
        try:
            # Create market context query
            symbols_text = ", ".join(symbols[:5])  # Limit to top 5 symbols
            query = f"""
            Current market conditions and sentiment analysis for active trading symbols: {symbols_text}
            
            Focus on:
            1. Breaking news affecting these symbols
            2. Sector-wide trends and momentum
            3. Market sentiment indicators
            4. Institutional activity patterns
            5. Risk factors and opportunities
            """
            
            # Get live search results
            search_result = await self.grok_engine.grok_client.make_live_search_request(
                query=query,
                search_config="market_analysis"
            )
            
            if search_result.success:
                return {
                    'market_context': search_result.content,
                    'sources_used': search_result.search_sources_used,
                    'citations': search_result.citations,
                    'confidence': search_result.confidence,
                    'enhanced': True,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {"context": "Live search unavailable", "enhanced": False}
                
        except Exception as e:
            logger.error(f"Enhanced market context analysis failed: {e}")
            return {"context": f"Analysis error: {str(e)}", "enhanced": False}
    
    async def intelligent_symbol_prioritization(self, symbols: List[str]) -> List[str]:
        """Intelligently prioritize symbols using Grok reasoning"""
        if not self.live_search_enabled or len(symbols) <= 10:
            return symbols  # No need to prioritize small lists
        
        try:
            # Get market context for prioritization
            context = await self.enhanced_market_context_analysis(symbols[:20])
            
            if context.get('enhanced'):
                # Use Grok reasoning to prioritize symbols
                prioritization_prompt = f"""
                Based on current market conditions, prioritize these trading symbols for scanning:
                
                SYMBOLS: {', '.join(symbols[:20])}
                
                MARKET CONTEXT:
                {context.get('market_context', '')[:1000]}
                
                Prioritize based on:
                1. News catalyst potential
                2. Technical setup quality
                3. Volume and liquidity
                4. Sector momentum
                5. Risk/reward opportunity
                
                Return top 15 symbols in priority order.
                """
                
                reasoning_result = await self.grok_engine.enhanced_market_reasoning(
                    market_scenario=prioritization_prompt,
                    reasoning_effort="low"  # Fast prioritization
                )
                
                if reasoning_result.grok_enhancement.success:
                    # Extract prioritized symbols from response
                    prioritized = self._extract_symbols_from_response(
                        reasoning_result.grok_enhancement.content, 
                        symbols
                    )
                    if prioritized:
                        logger.info(f"Grok prioritized {len(prioritized)} symbols for scanning")
                        return prioritized
            
            # Fallback to original order
            return symbols
            
        except Exception as e:
            logger.error(f"Symbol prioritization failed: {e}")
            return symbols
    
    def _extract_symbols_from_response(self, response: str, original_symbols: List[str]) -> List[str]:
        """Extract and validate symbols from Grok response"""
        try:
            # Simple extraction - look for symbol patterns in response
            found_symbols = []
            response_upper = response.upper()
            
            for symbol in original_symbols:
                if symbol.upper() in response_upper:
                    found_symbols.append(symbol)
            
            # Return found symbols + remaining original symbols
            remaining = [s for s in original_symbols if s not in found_symbols]
            return found_symbols + remaining
            
        except Exception as e:
            logger.error(f"Symbol extraction failed: {e}")
            return original_symbols

class GrokEnhancedAIEngine(AtlasAIEngine):
    """AI Engine enhanced with advanced Grok capabilities"""
    
    def __init__(self):
        super().__init__()
        self.grok_engine = None
        self.grok_enhanced = False
        
    async def initialize_grok_enhancement(self):
        """Initialize Grok enhancement for AI engine"""
        try:
            self.grok_engine = AtlasGrokIntegrationEngine()
            success = await self.grok_engine.initialize()
            
            if success:
                self.grok_enhanced = True
                logger.info("✅ AI Engine enhanced with advanced Grok capabilities")
            else:
                logger.warning("⚠️ Grok enhancement unavailable for AI engine")
                
        except Exception as e:
            logger.error(f"Failed to initialize Grok enhancement: {e}")
    
    async def enhanced_stock_analysis(self, symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Enhanced stock analysis with Grok intelligence"""
        if not self.grok_enhanced:
            return await self.standard_stock_analysis(symbol, analysis_type)
        
        try:
            # Multi-modal analysis combining live search, reasoning, and structured outputs
            
            # Step 1: Live search for current news and sentiment
            news_result = await self.grok_engine.grok_client.make_live_search_request(
                query=f"Latest news, earnings, and analysis for {symbol} stock",
                search_config="real_time_news",
                symbol=symbol
            )
            
            # Step 2: Enhanced reasoning analysis
            reasoning_prompt = f"""
            Comprehensive stock analysis for {symbol}:
            
            RECENT NEWS CONTEXT:
            {news_result.content[:1000] if news_result.success else 'No recent news available'}
            
            Provide detailed analysis covering:
            1. **Fundamental Analysis**: Financial health, growth prospects, valuation
            2. **Technical Analysis**: Chart patterns, momentum, support/resistance
            3. **Sentiment Analysis**: Market sentiment, institutional activity
            4. **Risk Assessment**: Key risks and mitigation strategies
            5. **Trading Strategy**: Entry/exit points, position sizing
            6. **Price Targets**: Short-term and long-term price objectives
            """
            
            reasoning_result = await self.grok_engine.enhanced_market_reasoning(
                market_scenario=reasoning_prompt,
                symbol=symbol,
                reasoning_effort="high"
            )
            
            # Step 3: Structured output generation
            structured_result = await self.grok_engine.grok_client.make_structured_analysis_request(
                symbol=symbol,
                analysis_type="market_analysis"
            )
            
            # Combine results
            return {
                'symbol': symbol,
                'analysis_type': analysis_type,
                'news_context': {
                    'content': news_result.content if news_result.success else None,
                    'sources': news_result.citations if news_result.success else [],
                    'sources_count': news_result.search_sources_used if news_result.success else 0
                },
                'reasoning_analysis': {
                    'content': reasoning_result.grok_enhancement.content,
                    'confidence': reasoning_result.combined_confidence,
                    'reasoning_steps': reasoning_result.reasoning_chain,
                    'has_reasoning_trace': reasoning_result.grok_enhancement.reasoning_content is not None
                },
                'structured_analysis': structured_result.structured_output if structured_result.success else None,
                'overall_confidence': self._calculate_overall_confidence([
                    news_result.confidence if news_result.success else 0.5,
                    reasoning_result.combined_confidence,
                    structured_result.confidence if structured_result.success else 0.5
                ]),
                'grok_enhanced': True,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Enhanced stock analysis failed for {symbol}: {e}")
            return await self.standard_stock_analysis(symbol, analysis_type)
    
    async def standard_stock_analysis(self, symbol: str, analysis_type: str) -> Dict[str, Any]:
        """Standard stock analysis fallback"""
        return {
            'symbol': symbol,
            'analysis_type': analysis_type,
            'analysis': f"Standard analysis for {symbol}",
            'confidence': 0.7,
            'grok_enhanced': False,
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_overall_confidence(self, confidences: List[float]) -> float:
        """Calculate weighted overall confidence"""
        if not confidences:
            return 0.5
        
        # Weight recent results more heavily
        weights = [0.4, 0.4, 0.2][:len(confidences)]
        weighted_sum = sum(c * w for c, w in zip(confidences, weights))
        total_weight = sum(weights[:len(confidences)])
        
        return weighted_sum / total_weight if total_weight > 0 else 0.5

class GrokSystemIntegrationManager:
    """Main integration manager for Grok system enhancements"""
    
    def __init__(self):
        self.enhanced_components = {}
        self.integration_status = {}
        
    async def initialize_all_enhancements(self, orchestrator=None):
        """Initialize all Grok enhancements across A.T.L.A.S. components"""
        logger.info("🚀 Initializing comprehensive Grok system integration...")
        
        try:
            # Initialize enhanced Lee Method scanner
            if orchestrator and hasattr(orchestrator, 'lee_method_scanner'):
                enhanced_lee = GrokEnhancedLeeMethodScanner(
                    orchestrator.lee_method_scanner.fmp_api_key,
                    orchestrator.lee_method_scanner.market_engine
                )
                await enhanced_lee.initialize_grok_enhancement()
                self.enhanced_components['lee_method'] = enhanced_lee
                self.integration_status['lee_method'] = enhanced_lee.grok_enhanced
            
            # Initialize enhanced real-time scanner
            enhanced_scanner = GrokEnhancedRealtimeScanner()
            await enhanced_scanner.initialize_grok_enhancement()
            self.enhanced_components['realtime_scanner'] = enhanced_scanner
            self.integration_status['realtime_scanner'] = enhanced_scanner.live_search_enabled
            
            # Initialize enhanced AI engine
            enhanced_ai = GrokEnhancedAIEngine()
            await enhanced_ai.initialize_grok_enhancement()
            self.enhanced_components['ai_engine'] = enhanced_ai
            self.integration_status['ai_engine'] = enhanced_ai.grok_enhanced
            
            # Log integration status
            enhanced_count = sum(1 for status in self.integration_status.values() if status)
            total_count = len(self.integration_status)
            
            logger.info(f"✅ Grok integration complete: {enhanced_count}/{total_count} components enhanced")
            
            return self.integration_status
            
        except Exception as e:
            logger.error(f"Grok system integration failed: {e}")
            return {}
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive integration status"""
        return {
            'components': self.integration_status,
            'enhanced_count': sum(1 for status in self.integration_status.values() if status),
            'total_count': len(self.integration_status),
            'overall_status': 'enhanced' if any(self.integration_status.values()) else 'standard',
            'timestamp': datetime.now().isoformat()
        }
