"""
A.T.L.A.S. Ultra-Responsive Alert Manager
Handles instant alert generation and delivery for TTM Squeeze pattern detection
Provides WebSocket real-time notifications, visual/audio alerts, and multi-channel delivery
"""

import asyncio
import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

logger = logging.getLogger(__name__)

class AlertPriority(Enum):
    """Alert priority levels"""
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

class AlertType(Enum):
    """Alert types"""
    FIRST_LESS_NEGATIVE = "first_less_negative"
    TTM_SQUEEZE_MOMENTUM = "ttm_squeeze_momentum"
    PATTERN_CONFIRMATION = "pattern_confirmation"
    SYSTEM_STATUS = "system_status"

@dataclass
class TTMSqueezeAlert:
    """TTM Squeeze pattern alert data structure"""
    alert_id: str
    symbol: str
    alert_type: AlertType
    priority: AlertPriority
    timestamp: datetime

    # Pattern data
    signal_type: str
    confidence: float
    signal_strength: str

    # Market data
    current_price: float
    price_change: float
    price_change_percent: float

    # Technical indicators
    histogram_current: float
    histogram_previous: float
    improvement_magnitude: float
    ema8_rising: bool
    ema21_rising: bool
    squeeze_active: bool

    # Alert metadata
    alert_message: str
    context_info: Dict[str, Any]
    delivery_channels: List[str]
    expires_at: datetime

    # Optional fields (must come last)
    volume: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['expires_at'] = self.expires_at.isoformat()
        data['alert_type'] = self.alert_type.value
        data['priority'] = self.priority.value
        return data

class AtlasAlertManager:
    """
    Ultra-responsive alert manager for TTM Squeeze pattern detection
    Provides instant alert generation and multi-channel delivery
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Alert storage
        self.active_alerts: Dict[str, TTMSqueezeAlert] = {}
        self.alert_history: List[TTMSqueezeAlert] = []
        self.alert_cooldowns: Dict[str, datetime] = {}  # Symbol -> last alert time
        
        # WebSocket connections
        self.websocket_connections: Set = set()
        
        # Alert configuration
        self.cooldown_period = 30  # seconds between alerts for same symbol
        self.max_alerts_per_minute = 20
        self.alert_expiry_minutes = 60
        
        # Performance tracking
        self.alerts_sent_count = 0
        self.alerts_per_minute = 0
        self.last_minute_reset = datetime.now()
        
        # Integration with News Insights
        self.news_insights_enabled = True
        self.news_insights_callbacks: List = []
        
        self.logger.info("[ALERT_MANAGER] Ultra-responsive alert manager initialized")

    async def generate_ttm_squeeze_alert(self, symbol: str, pattern_result: Dict[str, Any], 
                                       market_data: Dict[str, Any]) -> Optional[TTMSqueezeAlert]:
        """Generate TTM Squeeze pattern alert with ultra-responsive timing"""
        try:
            # Check cooldown to prevent spam
            if not self._check_alert_cooldown(symbol):
                return None
            
            # Check rate limiting
            if not self._check_rate_limit():
                self.logger.warning(f"[ALERT] Rate limit exceeded, skipping alert for {symbol}")
                return None
            
            # Determine alert priority and type
            alert_type, priority = self._determine_alert_type_and_priority(pattern_result)
            
            # Create alert
            alert = TTMSqueezeAlert(
                alert_id=str(uuid.uuid4()),
                symbol=symbol,
                alert_type=alert_type,
                priority=priority,
                timestamp=datetime.now(),
                signal_type=pattern_result.get('signal_type', 'ttm_squeeze'),
                confidence=pattern_result.get('confidence', 0.5),
                signal_strength=pattern_result.get('signal_strength', 'MODERATE'),
                current_price=market_data.get('price', 0.0),
                price_change=market_data.get('change', 0.0),
                price_change_percent=market_data.get('change_percent', 0.0),
                volume=market_data.get('volume'),
                histogram_current=pattern_result.get('histogram_current', 0.0),
                histogram_previous=pattern_result.get('histogram_previous', 0.0),
                improvement_magnitude=pattern_result.get('improvement_magnitude', 0.0),
                ema8_rising=pattern_result.get('ema8_rising', False),
                ema21_rising=pattern_result.get('ema21_rising', False),
                squeeze_active=pattern_result.get('squeeze_active', False),
                alert_message=self._generate_alert_message(symbol, pattern_result, market_data),
                context_info=self._generate_context_info(pattern_result, market_data),
                delivery_channels=['websocket', 'ui_notification'],
                expires_at=datetime.now() + timedelta(minutes=self.alert_expiry_minutes)
            )
            
            # Store alert
            self.active_alerts[alert.alert_id] = alert
            self.alert_history.append(alert)
            self.alert_cooldowns[symbol] = datetime.now()
            
            # Deliver alert immediately
            await self._deliver_alert(alert)
            
            # Update performance metrics
            self._update_performance_metrics()
            
            self.logger.info(f"[ALERT] Generated {priority.value} priority alert for {symbol}: {alert.signal_type}")
            return alert
            
        except Exception as e:
            self.logger.error(f"Error generating TTM Squeeze alert for {symbol}: {e}")
            return None

    def _determine_alert_type_and_priority(self, pattern_result: Dict[str, Any]) -> tuple[AlertType, AlertPriority]:
        """Determine alert type and priority based on pattern characteristics"""
        try:
            signal_type = pattern_result.get('signal_type', '')
            confidence = pattern_result.get('confidence', 0.5)
            
            # First less negative pattern gets highest priority
            if signal_type == 'first_less_negative':
                return AlertType.FIRST_LESS_NEGATIVE, AlertPriority.CRITICAL
            
            # TTM Squeeze momentum shift
            if 'ttm_squeeze' in signal_type or 'momentum_shift' in signal_type:
                if confidence >= 0.8:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.HIGH
                elif confidence >= 0.6:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.MEDIUM
                else:
                    return AlertType.TTM_SQUEEZE_MOMENTUM, AlertPriority.LOW
            
            # Default
            return AlertType.PATTERN_CONFIRMATION, AlertPriority.MEDIUM
            
        except Exception as e:
            self.logger.error(f"Error determining alert type and priority: {e}")
            return AlertType.PATTERN_CONFIRMATION, AlertPriority.LOW

    def _generate_alert_message(self, symbol: str, pattern_result: Dict[str, Any], 
                              market_data: Dict[str, Any]) -> str:
        """Generate human-readable alert message"""
        try:
            signal_type = pattern_result.get('signal_type', 'pattern')
            confidence = pattern_result.get('confidence', 0.5)
            price = market_data.get('price', 0.0)
            change_percent = market_data.get('change_percent', 0.0)
            
            if signal_type == 'first_less_negative':
                return (f"🟡 FIRST LESS NEGATIVE: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"Histogram turning positive! Confidence: {confidence:.1%}")
            
            elif 'momentum_shift' in signal_type:
                return (f"📈 MOMENTUM SHIFT: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"TTM Squeeze momentum reversal detected! Confidence: {confidence:.1%}")
            
            else:
                return (f"🎯 PATTERN ALERT: {symbol} at ${price:.2f} ({change_percent:+.2f}%) - "
                       f"TTM Squeeze pattern confirmed! Confidence: {confidence:.1%}")
                
        except Exception as e:
            self.logger.error(f"Error generating alert message: {e}")
            return f"Pattern alert for {symbol}"

    def _generate_context_info(self, pattern_result: Dict[str, Any], 
                             market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate additional context information for the alert"""
        try:
            return {
                'pattern_details': pattern_result,
                'market_data': market_data,
                'detection_time': datetime.now().isoformat(),
                'alert_source': 'A.T.L.A.S. TTM Squeeze Scanner',
                'recommended_action': self._get_recommended_action(pattern_result),
                'risk_level': self._assess_risk_level(pattern_result)
            }
        except Exception as e:
            self.logger.error(f"Error generating context info: {e}")
            return {}

    def _get_recommended_action(self, pattern_result: Dict[str, Any]) -> str:
        """Get recommended trading action based on pattern"""
        try:
            confidence = pattern_result.get('confidence', 0.5)
            signal_type = pattern_result.get('signal_type', '')
            
            if signal_type == 'first_less_negative':
                return "IMMEDIATE ATTENTION - First momentum shift detected"
            elif confidence >= 0.8:
                return "STRONG BUY SIGNAL - High confidence pattern"
            elif confidence >= 0.6:
                return "BUY SIGNAL - Moderate confidence pattern"
            else:
                return "WATCH - Low confidence pattern"
                
        except Exception as e:
            self.logger.error(f"Error getting recommended action: {e}")
            return "MONITOR"

    def _assess_risk_level(self, pattern_result: Dict[str, Any]) -> str:
        """Assess risk level for the pattern"""
        try:
            confidence = pattern_result.get('confidence', 0.5)
            
            if confidence >= 0.8:
                return "LOW"
            elif confidence >= 0.6:
                return "MEDIUM"
            else:
                return "HIGH"
                
        except Exception as e:
            self.logger.error(f"Error assessing risk level: {e}")
            return "MEDIUM"

    async def _deliver_alert(self, alert: TTMSqueezeAlert):
        """Deliver alert through all configured channels"""
        try:
            # Primary delivery channels
            delivery_tasks = []

            # WebSocket delivery (primary channel for real-time updates)
            delivery_tasks.append(self._send_websocket_alert(alert))

            # News Insights integration
            if self.news_insights_enabled:
                delivery_tasks.append(self._send_to_news_insights(alert))

            # Additional delivery channels based on alert priority
            if alert.priority in [AlertPriority.CRITICAL, AlertPriority.HIGH]:
                # High priority alerts get additional delivery channels
                if 'email' in alert.delivery_channels:
                    delivery_tasks.append(self._send_email_alert(alert))

                if 'push' in alert.delivery_channels:
                    delivery_tasks.append(self._send_push_notification(alert))

                if 'webhook' in alert.delivery_channels:
                    delivery_tasks.append(self._send_webhook_alert(alert))

            # Execute all delivery tasks concurrently for speed
            if delivery_tasks:
                await asyncio.gather(*delivery_tasks, return_exceptions=True)

            self.logger.info(f"[ALERT] Delivered {alert.priority.value} alert for {alert.symbol} via {len(delivery_tasks)} channels")

        except Exception as e:
            self.logger.error(f"Error delivering alert {alert.alert_id}: {e}")

    async def _send_websocket_alert(self, alert: TTMSqueezeAlert):
        """Send alert to all WebSocket connections"""
        try:
            if not self.websocket_connections:
                return
            
            alert_data = {
                'type': 'ttm_squeeze_alert',
                'alert': alert.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
            
            # Send to all connected clients
            disconnected = set()
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(json.dumps(alert_data))
                except Exception:
                    disconnected.add(websocket)
            
            # Remove disconnected clients
            self.websocket_connections -= disconnected
            
            self.logger.debug(f"[ALERT] Sent WebSocket alert to {len(self.websocket_connections)} clients")
            
        except Exception as e:
            self.logger.error(f"Error sending WebSocket alert: {e}")

    async def _send_to_news_insights(self, alert: TTMSqueezeAlert):
        """Send alert to News Insights system with enhanced integration"""
        try:
            # Create News Insights compatible alert format
            news_insights_alert = {
                'type': 'ttm_squeeze_pattern',
                'symbol': alert.symbol,
                'priority': alert.priority.value,
                'title': f"TTM Squeeze Alert: {alert.symbol}",
                'message': alert.alert_message,
                'confidence': alert.confidence,
                'signal_strength': alert.signal_strength,
                'price': alert.current_price,
                'change_percent': alert.price_change_percent,
                'technical_data': {
                    'histogram_current': alert.histogram_current,
                    'histogram_previous': alert.histogram_previous,
                    'improvement_magnitude': alert.improvement_magnitude,
                    'ema8_rising': alert.ema8_rising,
                    'ema21_rising': alert.ema21_rising,
                    'squeeze_active': alert.squeeze_active
                },
                'timestamp': alert.timestamp.isoformat(),
                'source': 'A.T.L.A.S. Ultra-Responsive Scanner'
            }

            # Send to News Insights callbacks
            for callback in self.news_insights_callbacks:
                try:
                    await callback(news_insights_alert)
                except Exception as e:
                    self.logger.error(f"Error in News Insights callback: {e}")

        except Exception as e:
            self.logger.error(f"Error sending to News Insights: {e}")

    async def _send_email_alert(self, alert: TTMSqueezeAlert):
        """Send email alert via SMTP"""
        try:
            # Check if email configuration is available
            email_config = getattr(self.config, 'email_config', {})
            if not email_config.get('enabled', False):
                self.logger.debug(f"Email alerts disabled for {alert.symbol}")
                return

            # Import email libraries only when needed
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart

            # Create email message
            msg = MIMEMultipart()
            msg['From'] = email_config.get('from_email', '<EMAIL>')
            msg['To'] = email_config.get('to_email', '<EMAIL>')
            msg['Subject'] = f"A.T.L.A.S. Alert: {alert.symbol} - {alert.alert_type.value}"

            # Email body
            body = f"""
A.T.L.A.S. Trading Alert

Symbol: {alert.symbol}
Alert Type: {alert.alert_type.value}
Priority: {alert.priority.value}
Confidence: {alert.confidence:.2f}
Current Price: ${alert.current_price:.2f}
Target Price: ${alert.target_price:.2f}
Stop Loss: ${alert.stop_loss:.2f}

Message: {alert.message}

Generated at: {alert.timestamp}

This is an automated alert from A.T.L.A.S. Trading System.
"""
            msg.attach(MIMEText(body, 'plain'))

            # Send email if SMTP configuration is available
            smtp_config = email_config.get('smtp', {})
            if smtp_config.get('server') and smtp_config.get('port'):
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
                if smtp_config.get('use_tls', True):
                    server.starttls()
                if smtp_config.get('username') and smtp_config.get('password'):
                    server.login(smtp_config['username'], smtp_config['password'])

                text = msg.as_string()
                server.sendmail(msg['From'], msg['To'], text)
                server.quit()

                self.logger.info(f"Email alert sent for {alert.symbol}")
            else:
                self.logger.debug(f"SMTP not configured - email alert logged for {alert.symbol}")

        except Exception as e:
            self.logger.error(f"Error sending email alert: {e}")

    async def _send_push_notification(self, alert: TTMSqueezeAlert):
        """Send push notification via webhook or service"""
        try:
            # Check if push notification configuration is available
            push_config = getattr(self.config, 'push_config', {})
            if not push_config.get('enabled', False):
                self.logger.debug(f"Push notifications disabled for {alert.symbol}")
                return

            # Prepare notification payload
            notification_data = {
                'title': f'A.T.L.A.S. Alert: {alert.symbol}',
                'body': f'{alert.alert_type.value} - {alert.message}',
                'data': {
                    'symbol': alert.symbol,
                    'alert_type': alert.alert_type.value,
                    'priority': alert.priority.value,
                    'confidence': alert.confidence,
                    'current_price': alert.current_price,
                    'target_price': alert.target_price,
                    'stop_loss': alert.stop_loss,
                    'timestamp': alert.timestamp.isoformat()
                }
            }

            # Send via webhook if configured
            webhook_url = push_config.get('webhook_url')
            if webhook_url:
                import requests
                response = requests.post(
                    webhook_url,
                    json=notification_data,
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )
                if response.status_code == 200:
                    self.logger.info(f"Push notification sent for {alert.symbol}")
                else:
                    self.logger.warning(f"Push notification failed for {alert.symbol}: {response.status_code}")
            else:
                self.logger.debug(f"Push webhook not configured - notification logged for {alert.symbol}")

        except Exception as e:
            self.logger.error(f"Error sending push notification: {e}")

    async def _send_webhook_alert(self, alert: TTMSqueezeAlert):
        """Send webhook alert to external services"""
        try:
            # Webhook implementation for Slack, Discord, etc.
            webhook_data = {
                'text': f"🚨 A.T.L.A.S. TTM Squeeze Alert",
                'attachments': [{
                    'color': 'good' if alert.priority == AlertPriority.HIGH else 'warning',
                    'title': f"{alert.symbol} - {alert.signal_type.upper()}",
                    'text': alert.alert_message,
                    'fields': [
                        {'title': 'Price', 'value': f"${alert.current_price:.2f}", 'short': True},
                        {'title': 'Change', 'value': f"{alert.price_change_percent:+.2f}%", 'short': True},
                        {'title': 'Confidence', 'value': f"{alert.confidence:.1%}", 'short': True},
                        {'title': 'Signal Strength', 'value': alert.signal_strength, 'short': True}
                    ],
                    'timestamp': alert.timestamp.isoformat()
                }]
            }

            self.logger.debug(f"Webhook alert prepared for {alert.symbol}")
            # Actual webhook sending would be implemented here

        except Exception as e:
            self.logger.error(f"Error sending webhook alert: {e}")

    def _check_alert_cooldown(self, symbol: str) -> bool:
        """Check if enough time has passed since last alert for this symbol"""
        try:
            if symbol not in self.alert_cooldowns:
                return True
            
            last_alert = self.alert_cooldowns[symbol]
            time_since_last = (datetime.now() - last_alert).total_seconds()
            
            return time_since_last >= self.cooldown_period
            
        except Exception as e:
            self.logger.error(f"Error checking alert cooldown: {e}")
            return True

    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits for alerts"""
        try:
            current_time = datetime.now()
            
            # Reset counter every minute
            if (current_time - self.last_minute_reset).total_seconds() >= 60:
                self.alerts_per_minute = 0
                self.last_minute_reset = current_time
            
            return self.alerts_per_minute < self.max_alerts_per_minute
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {e}")
            return True

    def _update_performance_metrics(self):
        """Update alert performance metrics"""
        try:
            self.alerts_sent_count += 1
            self.alerts_per_minute += 1
            
        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {e}")

    def add_websocket_connection(self, websocket):
        """Add WebSocket connection for alert delivery"""
        self.websocket_connections.add(websocket)
        self.logger.debug(f"[ALERT] Added WebSocket connection, total: {len(self.websocket_connections)}")

    def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
        self.logger.debug(f"[ALERT] Removed WebSocket connection, total: {len(self.websocket_connections)}")

    def add_news_insights_callback(self, callback):
        """Add callback for News Insights integration"""
        self.news_insights_callbacks.append(callback)

    def get_alert_status(self) -> Dict[str, Any]:
        """Get current alert system status"""
        try:
            current_time = datetime.now()
            
            # Clean up expired alerts
            self._cleanup_expired_alerts()
            
            return {
                'active_alerts': len(self.active_alerts),
                'total_alerts_sent': self.alerts_sent_count,
                'alerts_per_minute': self.alerts_per_minute,
                'websocket_connections': len(self.websocket_connections),
                'cooldown_period': self.cooldown_period,
                'max_alerts_per_minute': self.max_alerts_per_minute,
                'news_insights_enabled': self.news_insights_enabled,
                'last_alert_time': max(self.alert_cooldowns.values()).isoformat() if self.alert_cooldowns else None
            }
            
        except Exception as e:
            self.logger.error(f"Error getting alert status: {e}")
            return {'error': str(e)}

    def _cleanup_expired_alerts(self):
        """Clean up expired alerts"""
        try:
            current_time = datetime.now()
            expired_alerts = [
                alert_id for alert_id, alert in self.active_alerts.items()
                if alert.expires_at < current_time
            ]
            
            for alert_id in expired_alerts:
                del self.active_alerts[alert_id]
                
            # Keep only last 1000 alerts in history
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
                
        except Exception as e:
            self.logger.error(f"Error cleaning up expired alerts: {e}")

# Global alert manager instance
alert_manager = AtlasAlertManager()
