"""
A.T.L.A.S. News Insights Engine
Real-time market-moving news analysis with functional progress tracking and optimized response flows
"""

import asyncio
import logging
import json
import hashlib
import re
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np

# Core imports
from models import EngineStatus
from config import settings
from atlas_progress_tracker import AtlasProgressTracker, OperationType

# External API imports with graceful fallbacks
try:
    import aiohttp
    import asyncpg
    import redis.asyncio as redis
    from transformers import pipeline
    from sklearn.cluster import KMeans
    from sentence_transformers import SentenceTransformer
    EXTERNAL_LIBS_AVAILABLE = True
except ImportError:
    EXTERNAL_LIBS_AVAILABLE = False

logger = logging.getLogger(__name__)

class NewsCategory(Enum):
    """A.T.L.A.S.-specific news categories"""
    FED_POLICY = "Fed_Policy"
    EARNINGS_SURPRISE = "Earnings_Surprise"
    GEOPOLITICAL_RISK = "Geopolitical_Risk"
    SECTOR_ROTATION = "Sector_Rotation"
    OPTIONS_FLOW = "Options_Flow"
    REGULATORY = "Regulatory"
    ECONOMIC_DATA = "Economic_Data"
    MARKET_STRUCTURE = "Market_Structure"
    GENERAL = "General"

class AlertSeverity(Enum):
    """Alert severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class NewsArticle:
    """News article data structure"""
    id: str
    title: str
    content: str
    source: str
    url: str
    published_at: datetime
    symbols: List[str]
    category: NewsCategory
    sentiment_score: float
    confidence: float
    market_impact_score: float
    source_reliability: float
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert NewsArticle to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "source": self.source,
            "url": self.url,
            "published_at": self.published_at.isoformat(),
            "symbols": self.symbols,
            "category": self.category.value,
            "sentiment_score": self.sentiment_score,
            "confidence": self.confidence,
            "market_impact_score": self.market_impact_score,
            "source_reliability": self.source_reliability,
            "metadata": self.metadata
        }

@dataclass
class NewsAlert:
    """News alert data structure"""
    id: str
    alert_type: str
    severity: AlertSeverity
    title: str
    description: str
    symbols: List[str]
    sentiment_score: float
    market_impact: float
    created_at: datetime
    expires_at: datetime
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert NewsAlert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "alert_type": self.alert_type,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "symbols": self.symbols,
            "sentiment_score": self.sentiment_score,
            "market_impact": self.market_impact,
            "created_at": self.created_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "metadata": self.metadata
        }

class AtlasNewsInsightsEngine:
    """
    Comprehensive News Insights Engine for A.T.L.A.S. Trading System
    
    Features:
    - Real-time news ingestion from multiple sources
    - Advanced sentiment analysis with DistilBERT
    - Market impact scoring and volatility prediction
    - Theme clustering and narrative detection
    - Real-time alerts with WebSocket integration
    - Optimized response flows (fast path vs full pipeline)
    """
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.config = {
            "enabled": settings.NEWS_INSIGHTS_ENABLED,
            "cache_ttl": settings.NEWS_CACHE_TTL,
            "sentiment_threshold": settings.NEWS_SENTIMENT_THRESHOLD,
            "volume_threshold": settings.NEWS_VOLUME_THRESHOLD,
            "impact_threshold": settings.NEWS_IMPACT_THRESHOLD
        }
        
        # API configurations with graceful fallbacks
        self.api_configs = {
            "fmp": {"api_key": settings.FMP_API_KEY, "available": bool(settings.FMP_API_KEY)},
            "alpha_vantage": {"api_key": settings.ALPHA_VANTAGE_NEWS_API_KEY, "available": bool(settings.ALPHA_VANTAGE_NEWS_API_KEY)},
            "bloomberg": {"api_key": settings.BLOOMBERG_API_KEY, "available": bool(settings.BLOOMBERG_API_KEY)},
            "reuters": {"api_key": settings.REUTERS_API_KEY, "available": bool(settings.REUTERS_API_KEY)},
            "fred": {"api_key": settings.FRED_API_KEY, "available": bool(settings.FRED_API_KEY)},
            "twitter": {"bearer_token": settings.TWITTER_BEARER_TOKEN, "available": bool(settings.TWITTER_BEARER_TOKEN)},
            "reddit": {
                "client_id": settings.REDDIT_CLIENT_ID,
                "client_secret": settings.REDDIT_CLIENT_SECRET,
                "available": bool(settings.REDDIT_CLIENT_ID and settings.REDDIT_CLIENT_SECRET)
            }
        }
        
        # Core components
        self.session = None
        self.redis_client = None
        self.db_pool = None
        self.sentiment_model = None
        self.embedding_model = None
        
        # Caching and state management
        self.news_cache = {}
        self.sentiment_cache = {}
        self.alert_history = {}
        self.active_alerts = {}
        
        # Progress tracking
        self.progress_tracker = None
        
        # Central Time timezone for A.T.L.A.S. compliance
        self.central_tz = timezone(timedelta(hours=-6))  # Central Time (CT)
        
        logger.info(f"[NEWS] News Insights Engine initialized - External libs: {EXTERNAL_LIBS_AVAILABLE}")

    async def initialize(self):
        """Initialize the News Insights Engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if not self.config["enabled"]:
                logger.info("[NEWS] News Insights Engine disabled in configuration")
                self.status = EngineStatus.ACTIVE
                return True
            
            if not EXTERNAL_LIBS_AVAILABLE:
                logger.warning("[NEWS] External libraries not available - running in fallback mode")
                self.status = EngineStatus.ACTIVE
                return False
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={"User-Agent": "ATLAS-v5.0-Trading-System"}
            )
            
            # Initialize Redis cache
            try:
                self.redis_client = redis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    decode_responses=True
                )
                await self.redis_client.ping()
                logger.info("[NEWS] Redis cache connected")
            except Exception as e:
                logger.warning(f"[NEWS] Redis connection failed: {e} - using memory cache")
                self.redis_client = None
            
            # Initialize sentiment analysis model
            await self._initialize_sentiment_model()
            
            # Initialize embedding model for semantic search
            await self._initialize_embedding_model()
            
            # Initialize database connection
            await self._initialize_database()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[NEWS] News Insights Engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[NEWS] Initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_sentiment_model(self):
        """Initialize sentiment analysis model"""
        try:
            if EXTERNAL_LIBS_AVAILABLE:
                # Use DistilBERT fine-tuned for financial sentiment
                self.sentiment_model = pipeline(
                    "sentiment-analysis",
                    model=settings.SENTIMENT_MODEL_PATH,
                    return_all_scores=True
                )
                logger.info("[NEWS] DistilBERT sentiment model loaded")
            else:
                logger.info("[NEWS] Using fallback sentiment analysis")
        except Exception as e:
            logger.error(f"[NEWS] Sentiment model initialization failed: {e}")
            self.sentiment_model = None

    async def _initialize_embedding_model(self):
        """Initialize embedding model for semantic search"""
        try:
            if EXTERNAL_LIBS_AVAILABLE:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                logger.info("[NEWS] Sentence transformer model loaded")
            else:
                logger.info("[NEWS] Using fallback embedding generation")
        except Exception as e:
            logger.error(f"[NEWS] Embedding model initialization failed: {e}")
            self.embedding_model = None

    async def _initialize_database(self):
        """Initialize PostgreSQL database connection"""
        try:
            if hasattr(settings, 'DATABASE_URL') and settings.DATABASE_URL:
                self.db_pool = await asyncpg.create_pool(settings.DATABASE_URL)
                await self._create_news_tables()
                logger.info("[NEWS] Database connection established")
            else:
                logger.warning("[NEWS] Database not configured - using memory storage")
        except Exception as e:
            logger.error(f"[NEWS] Database initialization failed: {e}")
            self.db_pool = None

    async def _create_news_tables(self):
        """Create news-related database tables"""
        if not self.db_pool:
            return
        
        async with self.db_pool.acquire() as conn:
            # News articles table with time-series optimization
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS news_articles (
                    id VARCHAR(255) PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    source VARCHAR(100) NOT NULL,
                    url TEXT,
                    published_at TIMESTAMPTZ NOT NULL,
                    symbols TEXT[],
                    category VARCHAR(50),
                    sentiment_score FLOAT,
                    confidence FLOAT,
                    market_impact_score FLOAT,
                    source_reliability FLOAT,
                    metadata JSONB,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    embedding VECTOR(384)  -- For semantic search
                );
                
                CREATE INDEX IF NOT EXISTS idx_news_published_at ON news_articles (published_at DESC);
                CREATE INDEX IF NOT EXISTS idx_news_symbols ON news_articles USING GIN (symbols);
                CREATE INDEX IF NOT EXISTS idx_news_category ON news_articles (category);
                CREATE INDEX IF NOT EXISTS idx_news_sentiment ON news_articles (sentiment_score);
            """)
            
            # News alerts table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS news_alerts (
                    id VARCHAR(255) PRIMARY KEY,
                    alert_type VARCHAR(100) NOT NULL,
                    severity VARCHAR(20) NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    symbols TEXT[],
                    sentiment_score FLOAT,
                    market_impact FLOAT,
                    created_at TIMESTAMPTZ DEFAULT NOW(),
                    expires_at TIMESTAMPTZ,
                    metadata JSONB
                );
                
                CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON news_alerts (created_at DESC);
                CREATE INDEX IF NOT EXISTS idx_alerts_severity ON news_alerts (severity);
                CREATE INDEX IF NOT EXISTS idx_alerts_symbols ON news_alerts USING GIN (symbols);
            """)

    # ============================================================================
    # GAP DETECTION AND WEB SEARCH INTEGRATION
    # ============================================================================

    def detect_information_gaps(self, articles: List[NewsArticle], symbols: List[str] = None) -> Dict[str, Any]:
        """
        Detect information gaps that warrant web search enhancement
        """
        try:
            gaps_detected = []
            should_search = False

            # Gap 1: Low confidence scores
            if articles:
                avg_confidence = sum(article.confidence for article in articles) / len(articles)
                if avg_confidence < settings.WEB_SEARCH_CONFIDENCE_THRESHOLD:
                    gaps_detected.append(f"Low average confidence: {avg_confidence:.2f}")
                    should_search = True

            # Gap 2: Insufficient article count
            if len(articles) < settings.WEB_SEARCH_MIN_ARTICLES_THRESHOLD:
                gaps_detected.append(f"Insufficient articles: {len(articles)} < {settings.WEB_SEARCH_MIN_ARTICLES_THRESHOLD}")
                should_search = True

            # Gap 3: Symbol-specific gaps
            if symbols:
                symbol_coverage = {}
                for article in articles:
                    for symbol in article.symbols:
                        if symbol in symbols:
                            symbol_coverage[symbol] = symbol_coverage.get(symbol, 0) + 1

                uncovered_symbols = [s for s in symbols if symbol_coverage.get(s, 0) == 0]
                low_coverage_symbols = [s for s in symbols if 0 < symbol_coverage.get(s, 0) < 2]

                if uncovered_symbols:
                    gaps_detected.append(f"No coverage for symbols: {uncovered_symbols}")
                    should_search = True

                if low_coverage_symbols:
                    gaps_detected.append(f"Low coverage for symbols: {low_coverage_symbols}")
                    should_search = True

            # Gap 4: Recency gaps (articles older than 24 hours)
            if articles:
                recent_articles = [
                    a for a in articles
                    if (datetime.now() - a.published_at).total_seconds() < 86400  # 24 hours
                ]
                if len(recent_articles) < len(articles) * 0.5:  # Less than 50% recent
                    gaps_detected.append("Lack of recent articles (< 24 hours)")
                    should_search = True

            # Gap 5: Source diversity
            if articles:
                unique_sources = set(article.source for article in articles)
                if len(unique_sources) < 3:  # Less than 3 different sources
                    gaps_detected.append(f"Limited source diversity: {len(unique_sources)} sources")
                    should_search = True

            return {
                "gaps_detected": gaps_detected,
                "should_trigger_web_search": should_search,
                "gap_count": len(gaps_detected),
                "articles_analyzed": len(articles),
                "symbols_requested": symbols or [],
                "recommendation": "Trigger web search" if should_search else "Sufficient information available"
            }

        except Exception as e:
            logger.error(f"[GAP_DETECTION] Failed: {e}")
            return {
                "gaps_detected": ["Gap detection failed"],
                "should_trigger_web_search": True,  # Default to search on error
                "gap_count": 1,
                "error": str(e)
            }

    # ============================================================================
    # DATA INGESTION PIPELINE
    # ============================================================================

    async def ingest_news_data(self, symbols: List[str] = None, progress_tracker: AtlasProgressTracker = None,
                              operation_id: str = None) -> Dict[str, Any]:
        """
        Comprehensive news data ingestion from multiple sources

        Args:
            symbols: List of stock symbols to focus on
            progress_tracker: Progress tracking instance
            operation_id: Operation ID for progress updates

        Returns:
            Dict containing ingested news data and statistics
        """
        try:
            if progress_tracker and operation_id:
                await progress_tracker.start_step(operation_id, 0)  # Connecting to APIs

            ingested_articles = []
            source_stats = {}

            # Parallel ingestion from all available sources
            ingestion_tasks = []

            # Financial Modeling Prep
            if self.api_configs["fmp"]["available"]:
                ingestion_tasks.append(self._ingest_fmp_news(symbols))

            # Alpha Vantage
            if self.api_configs["alpha_vantage"]["available"]:
                ingestion_tasks.append(self._ingest_alpha_vantage_news(symbols))

            # SEC EDGAR filings
            ingestion_tasks.append(self._ingest_sec_filings(symbols))

            # Social media sources
            if self.api_configs["twitter"]["available"]:
                ingestion_tasks.append(self._ingest_twitter_sentiment(symbols))

            if self.api_configs["reddit"]["available"]:
                ingestion_tasks.append(self._ingest_reddit_sentiment(symbols))

            if progress_tracker and operation_id:
                await progress_tracker.update_step_progress(operation_id, 0, 0.5, "Fetching from multiple sources...")

            # Execute all ingestion tasks
            results = await asyncio.gather(*ingestion_tasks, return_exceptions=True)

            if progress_tracker and operation_id:
                await progress_tracker.complete_step(operation_id, 0, True)
                await progress_tracker.start_step(operation_id, 1)  # Processing data

            # Process results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"[NEWS] Ingestion task {i} failed: {result}")
                    continue

                if isinstance(result, list):
                    ingested_articles.extend(result)
                    source_stats[f"source_{i}"] = len(result)

            # Remove duplicates based on content hash
            unique_articles = self._deduplicate_articles(ingested_articles)

            if progress_tracker and operation_id:
                await progress_tracker.update_step_progress(operation_id, 1, 0.7, f"Processed {len(unique_articles)} unique articles")

            # Store in database and cache
            await self._store_articles(unique_articles)

            if progress_tracker and operation_id:
                await progress_tracker.complete_step(operation_id, 1, True)
                await progress_tracker.start_step(operation_id, 2)  # SEC filings
                await progress_tracker.complete_step(operation_id, 2, True)
                await progress_tracker.start_step(operation_id, 3)  # Data validation
                await progress_tracker.complete_step(operation_id, 3, True)

            return {
                "success": True,
                "articles_ingested": len(unique_articles),
                "sources_used": len([s for s in self.api_configs.values() if s.get("available", False)]),
                "source_statistics": source_stats,
                "symbols_covered": symbols or [],
                "timestamp": datetime.now(self.central_tz).isoformat(),
                "articles": [a.to_dict() for a in unique_articles[:10]]  # Return first 10 for preview, serialized
            }

        except Exception as e:
            logger.error(f"[NEWS] News ingestion failed: {e}")
            if progress_tracker and operation_id:
                await progress_tracker.complete_step(operation_id, 0, False, str(e))
            return {"success": False, "error": str(e)}

    async def _ingest_fmp_news(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Ingest news from Financial Modeling Prep API"""
        try:
            articles = []
            api_key = self.api_configs["fmp"]["api_key"]

            if symbols:
                # Symbol-specific news
                for symbol in symbols[:5]:  # Limit to 5 symbols to avoid rate limits
                    url = f"https://financialmodelingprep.com/api/v3/stock_news"
                    params = {"apikey": api_key, "tickers": symbol, "limit": 20}

                    async with self.session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            for item in data:
                                article = self._parse_fmp_article(item, symbol)
                                if article:
                                    articles.append(article)
            else:
                # General market news
                url = f"https://financialmodelingprep.com/api/v3/stock_news"
                params = {"apikey": api_key, "limit": 50}

                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        for item in data:
                            article = self._parse_fmp_article(item)
                            if article:
                                articles.append(article)

            logger.info(f"[NEWS] FMP: Ingested {len(articles)} articles")
            return articles

        except Exception as e:
            logger.error(f"[NEWS] FMP ingestion failed: {e}")
            return []

    def _parse_fmp_article(self, data: Dict[str, Any], symbol: str = None) -> Optional[NewsArticle]:
        """Parse FMP news article data"""
        try:
            # Extract symbols from title and content
            symbols = []
            if symbol:
                symbols.append(symbol)

            # Basic symbol extraction from text
            text = f"{data.get('title', '')} {data.get('text', '')}"
            symbol_pattern = r'\b[A-Z]{1,5}\b'
            found_symbols = re.findall(symbol_pattern, text)
            symbols.extend([s for s in found_symbols if len(s) <= 5 and s not in symbols])

            # Generate article ID
            article_id = hashlib.md5(f"{data.get('url', '')}{data.get('publishedDate', '')}".encode()).hexdigest()

            return NewsArticle(
                id=article_id,
                title=data.get("title", ""),
                content=data.get("text", ""),
                source="FMP",
                url=data.get("url", ""),
                published_at=datetime.fromisoformat(data.get("publishedDate", "").replace("Z", "+00:00")),
                symbols=symbols[:10],  # Limit symbols
                category=self._classify_news_category(data.get("title", "")),
                sentiment_score=0.0,  # Will be calculated later
                confidence=0.0,
                market_impact_score=0.0,
                source_reliability=0.8,  # FMP is generally reliable
                metadata={"source_data": data}
            )

        except Exception as e:
            logger.error(f"[NEWS] Failed to parse FMP article: {e}")
            return None

    async def _ingest_sec_filings(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Ingest SEC EDGAR filings"""
        try:
            articles = []

            # SEC EDGAR API endpoint for recent filings
            url = "https://www.sec.gov/api/xbrl/companyfacts"
            headers = {"User-Agent": settings.SEC_EDGAR_USER_AGENT}

            # For now, return simulated SEC filing data
            # In production, implement full SEC EDGAR API integration
            if symbols:
                for symbol in symbols[:3]:  # Limit to avoid rate limits
                    article = NewsArticle(
                        id=f"sec_{symbol}_{int(datetime.now().timestamp())}",
                        title=f"SEC Filing Update for {symbol}",
                        content=f"Recent regulatory filing detected for {symbol}",
                        source="SEC_EDGAR",
                        url=f"https://www.sec.gov/edgar/search/#/q={symbol}",
                        published_at=datetime.now(self.central_tz),
                        symbols=[symbol],
                        category=NewsCategory.REGULATORY,
                        sentiment_score=0.0,
                        confidence=0.7,
                        market_impact_score=0.6,
                        source_reliability=0.95,  # SEC is highly reliable
                        metadata={"filing_type": "8-K", "simulated": True}
                    )
                    articles.append(article)

            logger.info(f"[NEWS] SEC: Ingested {len(articles)} filings")
            return articles

        except Exception as e:
            logger.error(f"[NEWS] SEC ingestion failed: {e}")
            return []

    # ============================================================================
    # SENTIMENT ANALYSIS & MARKET IMPACT SCORING
    # ============================================================================

    async def analyze_sentiment(self, articles: List[NewsArticle], progress_tracker: AtlasProgressTracker = None,
                               operation_id: str = None) -> Dict[str, Any]:
        """
        Comprehensive sentiment analysis using DistilBERT and market impact scoring

        Args:
            articles: List of news articles to analyze
            progress_tracker: Progress tracking instance
            operation_id: Operation ID for progress updates

        Returns:
            Dict containing sentiment analysis results and market impact scores
        """
        try:
            if progress_tracker and operation_id:
                await progress_tracker.start_step(operation_id, 0)  # Text preprocessing

            # Detect information gaps and determine if web search is needed
            symbols = list(set(symbol for article in articles for symbol in article.symbols))
            gap_analysis = self.detect_information_gaps(articles, symbols)

            # Enhance with web search if gaps detected
            web_enhanced_articles = articles.copy()
            if gap_analysis["should_trigger_web_search"] and settings.WEB_SEARCH_NEWS_ENABLED:
                try:
                    from atlas_web_search_service import web_search_service, SearchContext
                    if web_search_service.is_available():
                        # Search for additional news to fill gaps
                        for symbol in symbols[:3]:  # Limit to avoid rate limits
                            search_results = await web_search_service.search_for_context(
                                f"{symbol} news sentiment market impact today",
                                SearchContext.NEWS_SENTIMENT,
                                [symbol],
                                max_results=2
                            )

                            # Convert search results to NewsArticle format
                            for result in search_results:
                                web_article = NewsArticle(
                                    id=f"web_{hashlib.md5(result.url.encode()).hexdigest()}",
                                    title=result.title,
                                    content=result.snippet,
                                    source=f"web_{result.source}",
                                    url=result.url,
                                    published_at=result.published_date or datetime.now(),
                                    symbols=[symbol],
                                    category=NewsCategory.MARKET_NEWS,
                                    sentiment_score=0.0,  # Will be calculated
                                    confidence=result.relevance_score / 10.0,
                                    market_impact_score=0.0,  # Will be calculated
                                    source_reliability=0.6,  # Web sources get lower reliability
                                    metadata={"web_enhanced": True, "original_search": True}
                                )
                                web_enhanced_articles.append(web_article)

                        logger.info(f"[NEWS_WEB_SEARCH] Enhanced with {len(web_enhanced_articles) - len(articles)} web articles")
                except Exception as e:
                    logger.error(f"[NEWS_WEB_SEARCH] Enhancement failed: {e}")
                    # Continue with original articles if web search fails

            analyzed_articles = []
            sentiment_stats = {"positive": 0, "negative": 0, "neutral": 0}

            for i, article in enumerate(articles):
                # Update progress
                if progress_tracker and operation_id and i % 10 == 0:
                    progress = min(0.9, i / len(articles))
                    await progress_tracker.update_step_progress(operation_id, 0, progress,
                                                              f"Processing article {i+1}/{len(articles)}")

                # Analyze sentiment
                sentiment_result = await self._analyze_article_sentiment(article)

                # Update article with sentiment data
                article.sentiment_score = sentiment_result["sentiment_score"]
                article.confidence = sentiment_result["confidence"]

                # Calculate market impact score
                article.market_impact_score = await self._calculate_market_impact(article)

                analyzed_articles.append(article)

                # Update statistics
                if sentiment_result["sentiment"] == "positive":
                    sentiment_stats["positive"] += 1
                elif sentiment_result["sentiment"] == "negative":
                    sentiment_stats["negative"] += 1
                else:
                    sentiment_stats["neutral"] += 1

            if progress_tracker and operation_id:
                await progress_tracker.complete_step(operation_id, 0, True)
                await progress_tracker.start_step(operation_id, 1)  # DistilBERT analysis
                await progress_tracker.complete_step(operation_id, 1, True)
                await progress_tracker.start_step(operation_id, 2)  # Confidence scoring
                await progress_tracker.complete_step(operation_id, 2, True)
                await progress_tracker.start_step(operation_id, 3)  # Sentiment aggregation
                await progress_tracker.complete_step(operation_id, 3, True)

            # Calculate overall market sentiment
            total_articles = len(analyzed_articles)
            overall_sentiment = {
                "positive_ratio": sentiment_stats["positive"] / total_articles if total_articles > 0 else 0,
                "negative_ratio": sentiment_stats["negative"] / total_articles if total_articles > 0 else 0,
                "neutral_ratio": sentiment_stats["neutral"] / total_articles if total_articles > 0 else 0,
                "average_sentiment": np.mean([a.sentiment_score for a in analyzed_articles]) if analyzed_articles else 0,
                "average_impact": np.mean([a.market_impact_score for a in analyzed_articles]) if analyzed_articles else 0
            }

            return {
                "success": True,
                "articles_analyzed": len(analyzed_articles),
                "sentiment_statistics": sentiment_stats,
                "overall_sentiment": overall_sentiment,
                "high_impact_articles": [a.to_dict() for a in analyzed_articles if a.market_impact_score > self.config["impact_threshold"]],
                "timestamp": datetime.now(self.central_tz).isoformat(),
                "articles": [a.to_dict() for a in analyzed_articles]
            }

        except Exception as e:
            logger.error(f"[NEWS] Sentiment analysis failed: {e}")
            if progress_tracker and operation_id:
                await progress_tracker.complete_step(operation_id, 0, False, str(e))
            return {"success": False, "error": str(e)}

    async def _analyze_article_sentiment(self, article: NewsArticle) -> Dict[str, Any]:
        """Analyze sentiment of a single article"""
        try:
            # Combine title and content for analysis
            text = f"{article.title} {article.content[:500]}"  # Limit content length

            # Check cache first
            text_hash = hashlib.md5(text.encode()).hexdigest()
            if text_hash in self.sentiment_cache:
                return self.sentiment_cache[text_hash]

            if self.sentiment_model:
                # Use DistilBERT model
                results = self.sentiment_model(text)

                # Process results (DistilBERT returns list of scores)
                positive_score = 0
                negative_score = 0

                for result in results:
                    if result["label"] == "POSITIVE":
                        positive_score = result["score"]
                    elif result["label"] == "NEGATIVE":
                        negative_score = result["score"]

                # Calculate sentiment score (-1 to 1)
                sentiment_score = positive_score - negative_score
                confidence = max(positive_score, negative_score)

                if sentiment_score > 0.1:
                    sentiment = "positive"
                elif sentiment_score < -0.1:
                    sentiment = "negative"
                else:
                    sentiment = "neutral"

                result = {
                    "sentiment": sentiment,
                    "sentiment_score": sentiment_score,
                    "confidence": confidence,
                    "model": "DistilBERT"
                }
            else:
                # Fallback sentiment analysis
                result = await self._fallback_sentiment_analysis(text)

            # Cache result
            self.sentiment_cache[text_hash] = result
            return result

        except Exception as e:
            logger.error(f"[NEWS] Article sentiment analysis failed: {e}")
            return {"sentiment": "neutral", "sentiment_score": 0.0, "confidence": 0.5, "model": "fallback"}

    async def _fallback_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """Fallback sentiment analysis using keyword matching"""
        positive_words = ["bullish", "growth", "profit", "gain", "rise", "increase", "positive", "strong", "beat", "exceed"]
        negative_words = ["bearish", "loss", "decline", "fall", "decrease", "negative", "weak", "miss", "below", "concern"]

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        total_words = positive_count + negative_count
        if total_words == 0:
            return {"sentiment": "neutral", "sentiment_score": 0.0, "confidence": 0.5, "model": "keyword_fallback"}

        sentiment_score = (positive_count - negative_count) / max(total_words, 1)
        confidence = min(0.8, total_words / 10)  # Max confidence 0.8 for fallback

        if sentiment_score > 0.2:
            sentiment = "positive"
        elif sentiment_score < -0.2:
            sentiment = "negative"
        else:
            sentiment = "neutral"

        return {
            "sentiment": sentiment,
            "sentiment_score": sentiment_score,
            "confidence": confidence,
            "model": "keyword_fallback"
        }

    async def _calculate_market_impact(self, article: NewsArticle) -> float:
        """Calculate market impact score for an article"""
        try:
            impact_score = 0.0

            # Source reliability weight (0.0 - 1.0)
            impact_score += article.source_reliability * 0.3

            # Sentiment strength weight
            impact_score += abs(article.sentiment_score) * 0.3

            # Category weight
            category_weights = {
                NewsCategory.FED_POLICY: 0.9,
                NewsCategory.EARNINGS_SURPRISE: 0.8,
                NewsCategory.GEOPOLITICAL_RISK: 0.7,
                NewsCategory.REGULATORY: 0.7,
                NewsCategory.ECONOMIC_DATA: 0.6,
                NewsCategory.SECTOR_ROTATION: 0.5,
                NewsCategory.OPTIONS_FLOW: 0.4,
                NewsCategory.MARKET_STRUCTURE: 0.4,
                NewsCategory.GENERAL: 0.2
            }
            impact_score += category_weights.get(article.category, 0.2) * 0.2

            # Symbol count weight (more symbols = broader impact)
            symbol_weight = min(0.2, len(article.symbols) * 0.05)
            impact_score += symbol_weight

            # Recency weight (newer articles have higher impact)
            hours_old = (datetime.now(timezone.utc) - article.published_at).total_seconds() / 3600
            recency_weight = max(0.0, 1.0 - (hours_old / 24))  # Decay over 24 hours
            impact_score *= (0.5 + recency_weight * 0.5)

            return min(1.0, impact_score)  # Cap at 1.0

        except Exception as e:
            logger.error(f"[NEWS] Market impact calculation failed: {e}")
            return 0.5  # Default moderate impact

    # ============================================================================
    # UTILITY FUNCTIONS & HELPER METHODS
    # ============================================================================

    def _classify_news_category(self, title: str) -> NewsCategory:
        """Classify news article into A.T.L.A.S. categories"""
        title_lower = title.lower()

        # Fed policy keywords
        if any(word in title_lower for word in ["fed", "federal reserve", "interest rate", "monetary policy", "powell"]):
            return NewsCategory.FED_POLICY

        # Earnings keywords
        if any(word in title_lower for word in ["earnings", "quarterly", "revenue", "eps", "guidance"]):
            return NewsCategory.EARNINGS_SURPRISE

        # Geopolitical keywords
        if any(word in title_lower for word in ["war", "sanctions", "trade", "china", "russia", "geopolitical"]):
            return NewsCategory.GEOPOLITICAL_RISK

        # Regulatory keywords
        if any(word in title_lower for word in ["sec", "regulation", "compliance", "filing", "investigation"]):
            return NewsCategory.REGULATORY

        # Economic data keywords
        if any(word in title_lower for word in ["gdp", "inflation", "unemployment", "cpi", "ppi", "economic"]):
            return NewsCategory.ECONOMIC_DATA

        # Options flow keywords
        if any(word in title_lower for word in ["options", "calls", "puts", "volatility", "gamma"]):
            return NewsCategory.OPTIONS_FLOW

        # Sector rotation keywords
        if any(word in title_lower for word in ["sector", "rotation", "tech", "energy", "healthcare", "financials"]):
            return NewsCategory.SECTOR_ROTATION

        # Market structure keywords
        if any(word in title_lower for word in ["market structure", "trading", "liquidity", "volume"]):
            return NewsCategory.MARKET_STRUCTURE

        return NewsCategory.GENERAL

    def _deduplicate_articles(self, articles: List[NewsArticle]) -> List[NewsArticle]:
        """Remove duplicate articles based on content similarity"""
        if not articles:
            return []

        unique_articles = []
        seen_hashes = set()

        for article in articles:
            # Create content hash
            content_hash = hashlib.md5(f"{article.title}{article.url}".encode()).hexdigest()

            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_articles.append(article)

        return unique_articles

    async def _store_articles(self, articles: List[NewsArticle]):
        """Store articles in database and cache"""
        try:
            # Store in database if available
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    for article in articles:
                        await conn.execute("""
                            INSERT INTO news_articles
                            (id, title, content, source, url, published_at, symbols, category,
                             sentiment_score, confidence, market_impact_score, source_reliability, metadata)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                            ON CONFLICT (id) DO UPDATE SET
                                sentiment_score = EXCLUDED.sentiment_score,
                                confidence = EXCLUDED.confidence,
                                market_impact_score = EXCLUDED.market_impact_score
                        """,
                        article.id, article.title, article.content, article.source, article.url,
                        article.published_at, article.symbols, article.category.value,
                        article.sentiment_score, article.confidence, article.market_impact_score,
                        article.source_reliability, json.dumps(article.metadata))

            # Store in Redis cache if available
            if self.redis_client:
                for article in articles:
                    cache_key = f"news_article:{article.id}"
                    await self.redis_client.setex(
                        cache_key,
                        self.config["cache_ttl"],
                        json.dumps(asdict(article), default=str)
                    )

            # Store in memory cache as fallback
            for article in articles:
                self.news_cache[article.id] = article

            logger.info(f"[NEWS] Stored {len(articles)} articles")

        except Exception as e:
            logger.error(f"[NEWS] Failed to store articles: {e}")

    async def _ingest_twitter_sentiment(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Ingest sentiment from Twitter/X financial accounts"""
        try:
            # Placeholder for Twitter API integration
            # In production, implement Twitter API v2 integration
            articles = []

            if symbols:
                for symbol in symbols[:3]:
                    article = NewsArticle(
                        id=f"twitter_{symbol}_{int(datetime.now().timestamp())}",
                        title=f"Social Media Sentiment for {symbol}",
                        content=f"Aggregated Twitter sentiment analysis for {symbol}",
                        source="Twitter",
                        url=f"https://twitter.com/search?q=${symbol}",
                        published_at=datetime.now(self.central_tz),
                        symbols=[symbol],
                        category=NewsCategory.GENERAL,
                        sentiment_score=np.random.uniform(-0.5, 0.5),  # Simulated
                        confidence=0.6,
                        market_impact_score=0.3,
                        source_reliability=0.5,  # Social media is less reliable
                        metadata={"platform": "twitter", "simulated": True}
                    )
                    articles.append(article)

            logger.info(f"[NEWS] Twitter: Ingested {len(articles)} sentiment data")
            return articles

        except Exception as e:
            logger.error(f"[NEWS] Twitter ingestion failed: {e}")
            return []

    async def _ingest_reddit_sentiment(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Ingest sentiment from Reddit r/investing"""
        try:
            # Placeholder for Reddit API integration
            articles = []

            if symbols:
                for symbol in symbols[:3]:
                    article = NewsArticle(
                        id=f"reddit_{symbol}_{int(datetime.now().timestamp())}",
                        title=f"Reddit Discussion Sentiment for {symbol}",
                        content=f"Aggregated Reddit r/investing sentiment for {symbol}",
                        source="Reddit",
                        url=f"https://reddit.com/r/investing/search?q={symbol}",
                        published_at=datetime.now(self.central_tz),
                        symbols=[symbol],
                        category=NewsCategory.GENERAL,
                        sentiment_score=np.random.uniform(-0.3, 0.3),  # Simulated
                        confidence=0.5,
                        market_impact_score=0.2,
                        source_reliability=0.4,  # Reddit is less reliable
                        metadata={"platform": "reddit", "subreddit": "investing", "simulated": True}
                    )
                    articles.append(article)

            logger.info(f"[NEWS] Reddit: Ingested {len(articles)} sentiment data")
            return articles

        except Exception as e:
            logger.error(f"[NEWS] Reddit ingestion failed: {e}")
            return []

    async def _ingest_alpha_vantage_news(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Ingest news from Alpha Vantage API"""
        try:
            articles = []
            api_key = self.api_configs["alpha_vantage"]["api_key"]

            # Alpha Vantage news endpoint
            url = "https://www.alphavantage.co/query"
            params = {
                "function": "NEWS_SENTIMENT",
                "apikey": api_key,
                "limit": 50
            }

            if symbols:
                params["tickers"] = ",".join(symbols[:5])

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if "feed" in data:
                        for item in data["feed"]:
                            article = self._parse_alpha_vantage_article(item)
                            if article:
                                articles.append(article)

            logger.info(f"[NEWS] Alpha Vantage: Ingested {len(articles)} articles")
            return articles

        except Exception as e:
            logger.error(f"[NEWS] Alpha Vantage ingestion failed: {e}")
            return []

    def _parse_alpha_vantage_article(self, data: Dict[str, Any]) -> Optional[NewsArticle]:
        """Parse Alpha Vantage news article data"""
        try:
            # Extract symbols from ticker sentiment
            symbols = []
            if "ticker_sentiment" in data:
                for ticker_data in data["ticker_sentiment"]:
                    symbols.append(ticker_data.get("ticker", ""))

            article_id = hashlib.md5(f"{data.get('url', '')}{data.get('time_published', '')}".encode()).hexdigest()

            return NewsArticle(
                id=article_id,
                title=data.get("title", ""),
                content=data.get("summary", ""),
                source="AlphaVantage",
                url=data.get("url", ""),
                published_at=datetime.strptime(data.get("time_published", ""), "%Y%m%dT%H%M%S"),
                symbols=symbols[:10],
                category=self._classify_news_category(data.get("title", "")),
                sentiment_score=float(data.get("overall_sentiment_score", 0)),
                confidence=0.7,
                market_impact_score=0.0,  # Will be calculated later
                source_reliability=0.75,  # Alpha Vantage is reliable
                metadata={"source_data": data}
            )

        except Exception as e:
            logger.error(f"[NEWS] Failed to parse Alpha Vantage article: {e}")
            return None

    async def _get_raw_articles_for_sentiment(self, symbols: List[str] = None) -> List[NewsArticle]:
        """Get raw NewsArticle objects for sentiment analysis (internal method)"""
        try:
            ingested_articles = []

            # Parallel ingestion from all available sources
            ingestion_tasks = []

            # Financial Modeling Prep
            if self.api_configs["fmp"]["available"]:
                ingestion_tasks.append(self._ingest_fmp_news(symbols))

            # Alpha Vantage
            if self.api_configs["alpha_vantage"]["available"]:
                ingestion_tasks.append(self._ingest_alpha_vantage_news(symbols))

            # SEC EDGAR filings
            ingestion_tasks.append(self._ingest_sec_filings(symbols))

            # Social media sources
            if self.api_configs["twitter"]["available"]:
                ingestion_tasks.append(self._ingest_twitter_sentiment(symbols))

            # Reddit sentiment
            ingestion_tasks.append(self._ingest_reddit_sentiment(symbols))

            # Execute all ingestion tasks
            results = await asyncio.gather(*ingestion_tasks, return_exceptions=True)

            # Process results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"[NEWS] Ingestion task {i} failed: {result}")
                    continue

                if isinstance(result, list):
                    ingested_articles.extend(result)

            # Remove duplicates and return raw NewsArticle objects
            unique_articles = self._deduplicate_articles(ingested_articles)
            return unique_articles[:10]  # Return first 10 for analysis

        except Exception as e:
            logger.error(f"[NEWS] Failed to get raw articles: {e}")
            return []

    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.session:
                await self.session.close()

            if self.redis_client:
                await self.redis_client.close()

            if self.db_pool:
                await self.db_pool.close()

            logger.info("[NEWS] News Insights Engine cleanup completed")

        except Exception as e:
            logger.error(f"[NEWS] Cleanup failed: {e}")

# ============================================================================
# MAIN ENGINE CLASS FOR ORCHESTRATOR INTEGRATION
# ============================================================================

class AtlasNewsInsightsOrchestrator:
    """Main News Insights orchestrator for A.T.L.A.S. integration"""

    def __init__(self):
        self.news_engine = AtlasNewsInsightsEngine()
        self.status = EngineStatus.INITIALIZING

    async def initialize(self):
        """Initialize the News Insights orchestrator"""
        try:
            await self.news_engine.initialize()
            self.status = self.news_engine.status
            return self.status == EngineStatus.ACTIVE
        except Exception as e:
            logger.error(f"[NEWS] Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            return False

    async def get_news_insights(self, symbols: List[str] = None, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Get comprehensive news insights"""
        insights_result = await self.news_engine.ingest_news_data(symbols)
        # Serialize the result for JSON response
        return self._serialize_for_json(insights_result)

    async def analyze_news_sentiment(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Analyze news sentiment for symbols"""
        # Get raw articles (NewsArticle objects) for sentiment analysis
        raw_articles = await self.news_engine._get_raw_articles_for_sentiment(symbols)
        if raw_articles:
            sentiment_result = await self.news_engine.analyze_sentiment(raw_articles)
            # Serialize the result for JSON response
            return self._serialize_for_json(sentiment_result)
        return {"success": False, "error": "Failed to get articles for sentiment analysis"}



    def _serialize_for_json(self, obj: Any) -> Any:
        """Serialize objects for JSON response"""
        if isinstance(obj, NewsArticle):
            return obj.to_dict()
        elif isinstance(obj, NewsAlert):
            return obj.to_dict()
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        elif isinstance(obj, dict):
            return {k: self._serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Enum):
            return obj.value
        else:
            return obj

    async def cleanup(self):
        """Cleanup resources"""
        await self.news_engine.cleanup()
