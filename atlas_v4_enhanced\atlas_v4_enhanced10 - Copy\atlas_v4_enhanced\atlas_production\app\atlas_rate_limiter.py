"""
A.T.L.A.S. Advanced Rate Limiting Manager
Intelligent rate limiting, retry mechanisms, and API optimization for real-time scanning
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
import random
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class APIProvider(Enum):
    """API provider types"""
    FMP = "fmp"
    ALPACA = "alpaca"
    YFINANCE = "yfinance"
    POLYGON = "polygon"
    ALPHA_VANTAGE = "alpha_vantage"


@dataclass
class RateLimitConfig:
    """Rate limit configuration for API providers"""
    requests_per_minute: int = 60
    requests_per_second: int = 5
    burst_limit: int = 10
    cooldown_seconds: int = 60
    retry_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 30.0
    jitter: bool = True


@dataclass
class APIRequest:
    """API request tracking"""
    provider: APIProvider
    endpoint: str
    params: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    attempt: int = 0
    priority: int = 1  # 1=high, 2=medium, 3=low


class RequestQueue:
    """Priority-based request queue with intelligent scheduling"""
    
    def __init__(self):
        self.queues = {1: deque(), 2: deque(), 3: deque()}  # Priority queues
        self.lock = asyncio.Lock()
    
    async def put(self, request: APIRequest):
        """Add request to appropriate priority queue"""
        async with self.lock:
            self.queues[request.priority].append(request)
    
    async def get(self) -> Optional[APIRequest]:
        """Get next request based on priority"""
        async with self.lock:
            # Check high priority first, then medium, then low
            for priority in [1, 2, 3]:
                if self.queues[priority]:
                    return self.queues[priority].popleft()
            return None
    
    async def size(self) -> int:
        """Get total queue size"""
        async with self.lock:
            return sum(len(q) for q in self.queues.values())


class AtlasRateLimiter:
    """Advanced rate limiting manager with intelligent throttling and retry mechanisms"""
    
    def __init__(self):
        self.configs = {
            APIProvider.FMP: RateLimitConfig(
                requests_per_minute=250,  # FMP free tier
                requests_per_second=5,
                burst_limit=10,
                cooldown_seconds=60
            ),
            APIProvider.ALPACA: RateLimitConfig(
                requests_per_minute=200,  # Alpaca paper trading
                requests_per_second=10,
                burst_limit=20,
                cooldown_seconds=30
            ),
            APIProvider.YFINANCE: RateLimitConfig(
                requests_per_minute=100,  # Conservative for yfinance
                requests_per_second=2,
                burst_limit=5,
                cooldown_seconds=120
            ),
            APIProvider.POLYGON: RateLimitConfig(
                requests_per_minute=100,
                requests_per_second=5,
                burst_limit=10,
                cooldown_seconds=60
            ),
            APIProvider.ALPHA_VANTAGE: RateLimitConfig(
                requests_per_minute=5,  # Very limited free tier
                requests_per_second=1,
                burst_limit=2,
                cooldown_seconds=300
            )
        }
        
        # Request tracking
        self.request_history = defaultdict(deque)  # provider -> timestamps
        self.burst_counters = defaultdict(int)     # provider -> current burst count
        self.cooldown_until = defaultdict(float)   # provider -> cooldown end time
        self.failed_requests = defaultdict(int)    # provider -> failure count
        
        # Request queue and processing
        self.request_queue = RequestQueue()
        self.processing = False
        self.session_cache = {}
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'rate_limited_requests': 0,
            'average_response_time': 0.0,
            'cache_hits': 0
        }
        
        # Response cache
        self.cache = {}
        self.cache_ttl = {}
        self.default_cache_duration = 30  # 30 seconds default
        
    async def start_processing(self):
        """Start the request processing loop"""
        if not self.processing:
            self.processing = True
            asyncio.create_task(self._process_queue())
            logger.info("Rate limiter processing started")
    
    async def stop_processing(self):
        """Stop the request processing loop"""
        self.processing = False
        logger.info("Rate limiter processing stopped")
    
    def _is_rate_limited(self, provider: APIProvider) -> bool:
        """Check if provider is currently rate limited"""
        config = self.configs[provider]
        now = time.time()
        
        # Check cooldown
        if now < self.cooldown_until[provider]:
            return True
        
        # Check burst limit
        if self.burst_counters[provider] >= config.burst_limit:
            return True
        
        # Check requests per minute
        history = self.request_history[provider]
        minute_ago = now - 60
        while history and history[0] < minute_ago:
            history.popleft()
        
        if len(history) >= config.requests_per_minute:
            return True
        
        # Check requests per second
        second_ago = now - 1
        recent_requests = sum(1 for ts in history if ts > second_ago)
        if recent_requests >= config.requests_per_second:
            return True
        
        return False
    
    def _calculate_delay(self, provider: APIProvider, attempt: int) -> float:
        """Calculate delay for retry with exponential backoff"""
        config = self.configs[provider]
        
        # Base delay with exponential backoff
        delay = config.base_delay * (2 ** (attempt - 1))
        delay = min(delay, config.max_delay)
        
        # Add jitter to prevent thundering herd
        if config.jitter:
            delay += random.uniform(0, delay * 0.1)
        
        return delay
    
    def _get_cache_key(self, provider: APIProvider, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        # Sort params for consistent key generation
        sorted_params = json.dumps(params, sort_keys=True)
        return f"{provider.value}:{endpoint}:{sorted_params}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached response is still valid"""
        if cache_key not in self.cache:
            return False
        
        if cache_key not in self.cache_ttl:
            return False
        
        return time.time() < self.cache_ttl[cache_key]
    
    async def _get_session(self, provider: APIProvider) -> aiohttp.ClientSession:
        """Get or create HTTP session for provider"""
        if provider not in self.session_cache:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            connector = aiohttp.TCPConnector(limit=100, limit_per_host=20)
            self.session_cache[provider] = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={'User-Agent': 'A.T.L.A.S. Trading System v4.0'}
            )
        
        return self.session_cache[provider]
    
    async def _execute_request(self, request: APIRequest) -> Tuple[bool, Any]:
        """Execute API request with proper error handling"""
        provider = request.provider
        config = self.configs[provider]
        
        try:
            session = await self._get_session(provider)
            start_time = time.time()
            
            # Build full URL
            base_urls = {
                APIProvider.FMP: "https://financialmodelingprep.com/api/v3",
                APIProvider.ALPACA: "https://paper-api.alpaca.markets/v2",
                APIProvider.YFINANCE: "https://query1.finance.yahoo.com/v8",
                APIProvider.POLYGON: "https://api.polygon.io/v2",
                APIProvider.ALPHA_VANTAGE: "https://www.alphavantage.co/query"
            }
            
            url = f"{base_urls[provider]}/{request.endpoint.lstrip('/')}"
            
            async with session.get(url, params=request.params) as response:
                response_time = time.time() - start_time
                
                # Update metrics
                self.metrics['total_requests'] += 1
                self.metrics['average_response_time'] = (
                    (self.metrics['average_response_time'] * (self.metrics['total_requests'] - 1) + response_time) /
                    self.metrics['total_requests']
                )
                
                if response.status == 200:
                    data = await response.json()
                    self.metrics['successful_requests'] += 1
                    
                    # Cache successful response
                    cache_key = self._get_cache_key(provider, request.endpoint, request.params)
                    self.cache[cache_key] = data
                    self.cache_ttl[cache_key] = time.time() + self.default_cache_duration
                    
                    return True, data
                
                elif response.status == 429:  # Rate limited
                    self.metrics['rate_limited_requests'] += 1
                    logger.warning(f"Rate limited by {provider.value}: {response.status}")
                    
                    # Set cooldown
                    self.cooldown_until[provider] = time.time() + config.cooldown_seconds
                    return False, f"Rate limited: {response.status}"
                
                else:
                    self.metrics['failed_requests'] += 1
                    logger.error(f"API error from {provider.value}: {response.status}")
                    return False, f"API error: {response.status}"
        
        except asyncio.TimeoutError:
            self.metrics['failed_requests'] += 1
            logger.error(f"Timeout for {provider.value} request")
            return False, "Request timeout"
        
        except Exception as e:
            self.metrics['failed_requests'] += 1
            logger.error(f"Request error for {provider.value}: {e}")
            return False, str(e)
    
    async def _process_queue(self):
        """Process queued requests with rate limiting"""
        while self.processing:
            try:
                request = await self.request_queue.get()
                if not request:
                    await asyncio.sleep(0.1)
                    continue
                
                provider = request.provider
                config = self.configs[provider]
                
                # Check if we can process this request
                if self._is_rate_limited(provider):
                    # Put request back in queue with lower priority
                    request.priority = min(request.priority + 1, 3)
                    await self.request_queue.put(request)
                    await asyncio.sleep(0.5)
                    continue
                
                # Execute request
                success, result = await self._execute_request(request)
                
                if success:
                    # Update tracking
                    now = time.time()
                    self.request_history[provider].append(now)
                    self.burst_counters[provider] += 1
                    self.failed_requests[provider] = 0  # Reset failure count
                    
                    # Reset burst counter after burst window
                    if self.burst_counters[provider] >= config.burst_limit:
                        await asyncio.sleep(1.0)  # Brief pause after burst
                        self.burst_counters[provider] = 0
                
                else:
                    # Handle failure
                    self.failed_requests[provider] += 1
                    request.attempt += 1
                    
                    if request.attempt < config.retry_attempts:
                        # Retry with exponential backoff
                        delay = self._calculate_delay(provider, request.attempt)
                        logger.info(f"Retrying {provider.value} request in {delay:.2f}s (attempt {request.attempt})")
                        
                        await asyncio.sleep(delay)
                        await self.request_queue.put(request)
                    else:
                        logger.error(f"Max retries exceeded for {provider.value} request")
                
                # Small delay between requests
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in request processing: {e}")
                await asyncio.sleep(1.0)
    
    async def make_request(self, provider: APIProvider, endpoint: str, params: Dict[str, Any], 
                          priority: int = 1, cache_duration: Optional[int] = None) -> Optional[Any]:
        """Make rate-limited API request with caching"""
        
        # Check cache first
        cache_key = self._get_cache_key(provider, endpoint, params)
        if self._is_cache_valid(cache_key):
            self.metrics['cache_hits'] += 1
            logger.debug(f"Cache hit for {provider.value} request")
            return self.cache[cache_key]
        
        # Create request
        request = APIRequest(
            provider=provider,
            endpoint=endpoint,
            params=params,
            priority=priority
        )
        
        # Add to queue
        await self.request_queue.put(request)
        
        # Wait for processing (with timeout)
        timeout = 30.0  # 30 second timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]
            await asyncio.sleep(0.1)
        
        logger.warning(f"Request timeout for {provider.value}")
        return None
    
    async def batch_request(self, requests: List[Tuple[APIProvider, str, Dict[str, Any]]], 
                           priority: int = 2) -> List[Optional[Any]]:
        """Execute multiple requests efficiently"""
        
        # Add all requests to queue
        for provider, endpoint, params in requests:
            request = APIRequest(
                provider=provider,
                endpoint=endpoint,
                params=params,
                priority=priority
            )
            await self.request_queue.put(request)
        
        # Wait for all responses
        results = []
        timeout = 60.0  # 60 second timeout for batch
        start_time = time.time()
        
        for provider, endpoint, params in requests:
            cache_key = self._get_cache_key(provider, endpoint, params)
            
            # Wait for this specific response
            while time.time() - start_time < timeout:
                if self._is_cache_valid(cache_key):
                    results.append(self.cache[cache_key])
                    break
                await asyncio.sleep(0.1)
            else:
                results.append(None)  # Timeout
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            'queue_size': asyncio.create_task(self.request_queue.size()),
            'cache_size': len(self.cache),
            'providers_in_cooldown': len([p for p, t in self.cooldown_until.items() if time.time() < t])
        }
    
    async def cleanup(self):
        """Cleanup resources"""
        await self.stop_processing()
        
        # Close all sessions
        for session in self.session_cache.values():
            await session.close()
        
        self.session_cache.clear()
        self.cache.clear()
        self.cache_ttl.clear()


# Global rate limiter instance
rate_limiter = AtlasRateLimiter()
