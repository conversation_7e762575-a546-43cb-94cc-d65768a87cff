"""
A.T.L.A.S. Secrets Management System
Secure handling of API keys and sensitive configuration
"""

import os
import logging
import json
import base64
from typing import Dict, Optional, Any
from pathlib import Path
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

logger = logging.getLogger(__name__)


class SecretsManager:
    """Secure secrets management for A.T.L.A.S. system"""
    
    def __init__(self, secrets_file: str = "atlas_secrets.enc"):
        self.secrets_file = Path(secrets_file)
        self.key = None
        self._secrets_cache = {}
        
    def _generate_key(self, password: str, salt: bytes = None) -> bytes:
        """Generate encryption key from password"""
        if salt is None:
            salt = os.urandom(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt
    
    def initialize_secrets_file(self, master_password: str) -> bool:
        """Initialize encrypted secrets file"""
        try:
            key, salt = self._generate_key(master_password)
            self.key = key
            
            # Create initial empty secrets
            initial_secrets = {
                "api_keys": {},
                "database_urls": {},
                "encryption_keys": {},
                "metadata": {
                    "created": str(Path(__file__).stat().st_mtime),
                    "version": "1.0"
                }
            }
            
            # Encrypt and save
            fernet = Fernet(key)
            encrypted_data = fernet.encrypt(json.dumps(initial_secrets).encode())
            
            # Save salt + encrypted data
            with open(self.secrets_file, 'wb') as f:
                f.write(salt + encrypted_data)
            
            logger.info("Secrets file initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize secrets file: {e}")
            return False
    
    def unlock_secrets(self, master_password: str) -> bool:
        """Unlock encrypted secrets file"""
        try:
            if not self.secrets_file.exists():
                logger.warning("Secrets file does not exist. Using environment variables only.")
                return False
            
            with open(self.secrets_file, 'rb') as f:
                data = f.read()
            
            # Extract salt and encrypted data
            salt = data[:16]
            encrypted_data = data[16:]
            
            # Generate key and decrypt
            key, _ = self._generate_key(master_password, salt)
            fernet = Fernet(key)
            
            decrypted_data = fernet.decrypt(encrypted_data)
            self._secrets_cache = json.loads(decrypted_data.decode())
            self.key = key
            
            logger.info("Secrets unlocked successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unlock secrets: {e}")
            return False
    
    def get_secret(self, category: str, key: str, fallback_env: str = None) -> Optional[str]:
        """Get secret value with environment variable fallback"""
        try:
            # First try encrypted secrets
            if self._secrets_cache and category in self._secrets_cache:
                value = self._secrets_cache[category].get(key)
                if value:
                    return value
            
            # Fallback to environment variable
            if fallback_env:
                env_value = os.getenv(fallback_env)
                if env_value:
                    return env_value
            
            # Try direct environment variable with key name
            env_value = os.getenv(key.upper())
            if env_value:
                return env_value
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving secret {category}.{key}: {e}")
            return None
    
    def set_secret(self, category: str, key: str, value: str) -> bool:
        """Set secret value in encrypted storage"""
        try:
            if not self.key:
                logger.error("Secrets not unlocked. Cannot set secret.")
                return False
            
            if category not in self._secrets_cache:
                self._secrets_cache[category] = {}
            
            self._secrets_cache[category][key] = value
            
            # Re-encrypt and save
            fernet = Fernet(self.key)
            encrypted_data = fernet.encrypt(json.dumps(self._secrets_cache).encode())
            
            # Read existing salt
            with open(self.secrets_file, 'rb') as f:
                salt = f.read(16)
            
            # Write salt + new encrypted data
            with open(self.secrets_file, 'wb') as f:
                f.write(salt + encrypted_data)
            
            logger.info(f"Secret {category}.{key} updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set secret {category}.{key}: {e}")
            return False
    
    def get_api_key(self, provider: str) -> Optional[str]:
        """Get API key for specific provider"""
        key_mappings = {
            'alpaca': ('api_keys', 'alpaca_api_key', 'ALPACA_API_KEY'),
            'alpaca_secret': ('api_keys', 'alpaca_secret_key', 'ALPACA_SECRET_KEY'),
            'fmp': ('api_keys', 'fmp_api_key', 'FMP_API_KEY'),
            'openai': ('api_keys', 'openai_api_key', 'OPENAI_API_KEY'),
            'grok': ('api_keys', 'grok_api_key', 'GROK_API_KEY'),
            'predicto': ('api_keys', 'predicto_api_key', 'PREDICTO_API_KEY'),
            'google_search': ('api_keys', 'google_search_api_key', 'GOOGLE_SEARCH_API_KEY'),
        }
        
        if provider in key_mappings:
            category, key, env_var = key_mappings[provider]
            return self.get_secret(category, key, env_var)
        
        return None
    
    def validate_api_keys(self) -> Dict[str, bool]:
        """Validate all API keys are present and properly formatted"""
        validation_results = {}
        
        providers = ['alpaca', 'alpaca_secret', 'fmp', 'openai', 'grok']
        
        for provider in providers:
            key = self.get_api_key(provider)
            validation_results[provider] = {
                'present': bool(key),
                'valid_format': self._validate_key_format(provider, key) if key else False
            }
        
        return validation_results
    
    def _validate_key_format(self, provider: str, key: str) -> bool:
        """Validate API key format for specific providers"""
        if not key:
            return False
        
        # Check for placeholder patterns
        placeholder_patterns = ['your_', 'placeholder', 'demo', 'test_key', 'example']
        if any(pattern in key.lower() for pattern in placeholder_patterns):
            return False
        
        # Provider-specific format validation
        format_rules = {
            'grok': lambda k: k.startswith('xai-') and len(k) > 10,
            'openai': lambda k: k.startswith('sk-') and len(k) > 20,
            'alpaca': lambda k: len(k) >= 20 and k.isalnum(),
            'alpaca_secret': lambda k: len(k) >= 40,
            'fmp': lambda k: len(k) >= 20 and k.isalnum(),
        }
        
        if provider in format_rules:
            return format_rules[provider](key)
        
        return len(key) >= 10  # Basic length check for other providers
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get summary of configuration status"""
        validation = self.validate_api_keys()
        
        return {
            'secrets_file_exists': self.secrets_file.exists(),
            'secrets_unlocked': bool(self.key),
            'api_keys_status': validation,
            'total_keys_configured': sum(1 for v in validation.values() if v['present']),
            'total_keys_valid': sum(1 for v in validation.values() if v['valid_format']),
            'environment_fallback_active': not bool(self.key)
        }


# Global secrets manager instance
secrets_manager = SecretsManager()


def get_api_key(provider: str) -> Optional[str]:
    """Convenience function to get API key"""
    return secrets_manager.get_api_key(provider)


def initialize_secrets_management(master_password: str = None) -> bool:
    """Initialize secrets management system"""
    try:
        # Try to use master password from environment if not provided
        if not master_password:
            master_password = os.getenv('ATLAS_MASTER_PASSWORD')
        
        if master_password:
            if not secrets_manager.secrets_file.exists():
                return secrets_manager.initialize_secrets_file(master_password)
            else:
                return secrets_manager.unlock_secrets(master_password)
        else:
            logger.info("No master password provided. Using environment variables only.")
            return True
            
    except Exception as e:
        logger.error(f"Failed to initialize secrets management: {e}")
        return False
