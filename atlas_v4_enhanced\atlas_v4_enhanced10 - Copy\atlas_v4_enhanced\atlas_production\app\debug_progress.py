#!/usr/bin/env python3
"""
Debug progress tracking to see the actual data structure
"""
import asyncio
import json
import websockets
import aiohttp
import time

async def debug_progress_data():
    session_id = f'debug_{int(time.time())}'
    ws_uri = f'ws://localhost:8001/ws/{session_id}'
    chat_url = 'http://localhost:8001/api/v1/chat'
    
    print(f'Debugging progress data with session: {session_id}')
    
    try:
        async with websockets.connect(ws_uri) as websocket:
            print('WebSocket connected')
            
            # Wait for connection confirmation
            message = await websocket.recv()
            print(f'Connection: {message}')
            
            # Make chat request
            chat_payload = {
                'message': 'What is TSLA doing?',
                'session_id': session_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(chat_url, json=chat_payload) as response:
                    print(f'Chat request sent, status: {response.status}')
            
            # Listen for first few progress updates and print raw data
            for i in range(5):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    data = json.loads(message)
                    
                    if data.get('type') == 'progress_update':
                        print(f'Raw Progress Update #{i+1}:')
                        print(json.dumps(data, indent=2))
                        print('---')
                        break
                        
                except asyncio.TimeoutError:
                    print('Timeout waiting for progress update')
                    break
                    
    except Exception as e:
        print(f'Debug failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(debug_progress_data())
