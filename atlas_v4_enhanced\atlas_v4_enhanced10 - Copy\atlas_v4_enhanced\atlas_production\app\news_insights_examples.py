"""
A.T.L.A.S. News Insights Module - Usage Examples
Demonstrates how to use the News Insights functionality
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

# Import A.T.L.A.S. components
from atlas_orchestrator import AtlasOrchestrator
from atlas_ai_core import AtlasAIEngine
from atlas_news_insights_engine import AtlasNewsInsightsEngine, AtlasNewsInsightsOrchestrator
from atlas_progress_tracker import AtlasProgressTracker, OperationType

class NewsInsightsExamples:
    """Examples demonstrating News Insights functionality"""
    
    def __init__(self):
        self.orchestrator = None
        self.ai_engine = None
        self.news_engine = None
    
    async def initialize(self):
        """Initialize A.T.L.A.S. system for examples"""
        print("🔧 Initializing A.T.L.A.S. system...")
        
        # Initialize orchestrator (includes news insights)
        self.orchestrator = AtlasOrchestrator()
        await self.orchestrator.initialize()
        
        # Get AI engine for conversational examples
        self.ai_engine = self.orchestrator.engines.get('ai')
        
        # Get news engine for direct API examples
        self.news_engine = self.orchestrator.engines.get('news_insights')
        
        print("✅ A.T.L.A.S. system initialized successfully")
    
    async def example_1_simple_news_query(self):
        """Example 1: Simple news query using fast path"""
        print("\n" + "="*60)
        print("📰 EXAMPLE 1: Simple News Query (Fast Path)")
        print("="*60)
        
        # Simple conversational query
        queries = [
            "Any market news today?",
            "What's happening in the markets?",
            "Latest news update please"
        ]
        
        for query in queries:
            print(f"\n👤 User: {query}")
            
            if self.ai_engine:
                response = await self.ai_engine.process_message(
                    message=query,
                    session_id="example_session_1",
                    orchestrator=self.orchestrator
                )
                
                print(f"🤖 A.T.L.A.S.: {response.response[:200]}...")
                print(f"⚡ Response Type: {response.type} (Fast Path)")
                print(f"🎯 Confidence: {response.confidence:.2f}")
            else:
                print("❌ AI Engine not available")
    
    async def example_2_comprehensive_analysis(self):
        """Example 2: Comprehensive news analysis using full pipeline"""
        print("\n" + "="*60)
        print("📊 EXAMPLE 2: Comprehensive Analysis (Full Pipeline)")
        print("="*60)
        
        # Complex analysis queries
        queries = [
            "Analyze comprehensive news sentiment for AAPL",
            "What's the market impact of recent Fed policy news?",
            "Detailed sentiment analysis for tech stocks"
        ]
        
        for query in queries:
            print(f"\n👤 User: {query}")
            
            if self.ai_engine:
                response = await self.ai_engine.process_message(
                    message=query,
                    session_id="example_session_2",
                    orchestrator=self.orchestrator
                )
                
                print(f"🤖 A.T.L.A.S.: {response.response[:300]}...")
                print(f"🔄 Response Type: {response.type} (Full Pipeline)")
                print(f"🎯 Confidence: {response.confidence:.2f}")
            else:
                print("❌ AI Engine not available")
    
    async def example_3_direct_api_usage(self):
        """Example 3: Direct API usage for programmatic access"""
        print("\n" + "="*60)
        print("🔌 EXAMPLE 3: Direct API Usage")
        print("="*60)
        
        if not self.orchestrator:
            print("❌ Orchestrator not available")
            return
        
        # Example 3a: Get news insights for specific symbols
        print("\n📈 Getting news insights for AAPL and MSFT...")
        try:
            news_data = await self.orchestrator.get_news_insights(
                symbols=["AAPL", "MSFT"],
                analysis_type="comprehensive"
            )
            
            if news_data.get("success"):
                print(f"✅ Successfully retrieved news data")
                print(f"📊 Articles analyzed: {news_data.get('articles_ingested', 0)}")
                print(f"🔗 Sources used: {news_data.get('sources_used', 0)}")
            else:
                print(f"❌ News data retrieval failed: {news_data.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        # Example 3b: Analyze sentiment for specific symbol
        print("\n🎭 Analyzing sentiment for AAPL...")
        try:
            sentiment_data = await self.orchestrator.analyze_news_sentiment(["AAPL"])
            
            if sentiment_data.get("success"):
                overall = sentiment_data.get("overall_sentiment", {})
                print(f"✅ Sentiment analysis completed")
                print(f"📊 Average sentiment: {overall.get('average_sentiment', 0):.2f}")
                print(f"📈 Average impact: {overall.get('average_impact', 0):.2f}")
                print(f"📰 Articles analyzed: {sentiment_data.get('articles_analyzed', 0)}")
            else:
                print(f"❌ Sentiment analysis failed: {sentiment_data.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"❌ Exception: {e}")
        
        # Example 3c: Get market news alerts
        print("\n🚨 Getting market news alerts...")
        try:
            alerts_data = await self.orchestrator.get_market_news_alerts(severity="medium")
            
            if alerts_data.get("success"):
                alerts = alerts_data.get("alerts", [])
                print(f"✅ Retrieved {len(alerts)} alerts")
                
                for alert in alerts[:3]:  # Show first 3 alerts
                    print(f"  🔔 {alert.get('title', 'No title')}")
                    print(f"     Severity: {alert.get('severity', 'unknown')}")
                    print(f"     Impact: {alert.get('impact_score', 0):.2f}")
            else:
                print(f"❌ Alerts retrieval failed: {alerts_data.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    async def example_4_progress_tracking(self):
        """Example 4: Progress tracking demonstration"""
        print("\n" + "="*60)
        print("📈 EXAMPLE 4: Progress Tracking")
        print("="*60)
        
        # Initialize progress tracker
        progress_tracker = AtlasProgressTracker()
        
        # Demonstrate news-specific operations
        news_operations = [
            (OperationType.NEWS_INGESTION, "News Data Ingestion"),
            (OperationType.NEWS_SENTIMENT_ANALYSIS, "Sentiment Analysis"),
            (OperationType.NEWS_COMPREHENSIVE_ANALYSIS, "Comprehensive Analysis")
        ]
        
        for op_type, description in news_operations:
            print(f"\n🔄 Starting {description}...")
            
            # Create operation
            operation_id = progress_tracker.create_operation(
                operation_type=op_type,
                session_id="example_session_4",
                title=description,
                description=f"Demonstrating {description.lower()}"
            )
            
            # Get operation details
            operation = progress_tracker.active_operations.get(operation_id)
            if operation:
                print(f"📋 Operation created with {len(operation.steps)} steps")
                
                # Simulate step progression
                for i, step in enumerate(operation.steps):
                    print(f"  ⏳ Step {i+1}: {step.name}")
                    
                    # Start step
                    await progress_tracker.start_step(operation_id, i)
                    await asyncio.sleep(0.2)  # Simulate processing
                    
                    # Update progress
                    await progress_tracker.update_step_progress(
                        operation_id, i, 0.5, "Processing..."
                    )
                    await asyncio.sleep(0.2)  # Simulate processing
                    
                    # Complete step
                    await progress_tracker.complete_step(operation_id, i, True)
                    print(f"  ✅ Step {i+1} completed")
                
                # Complete operation
                await progress_tracker.complete_operation(operation_id, True)
                print(f"🎉 {description} completed successfully!")
            else:
                print(f"❌ Failed to create operation")
    
    async def example_5_news_engine_status(self):
        """Example 5: Check news engine status and capabilities"""
        print("\n" + "="*60)
        print("🔍 EXAMPLE 5: News Engine Status")
        print("="*60)
        
        if not self.orchestrator:
            print("❌ Orchestrator not available")
            return
        
        try:
            status_data = await self.orchestrator.get_news_insights_status()
            
            print(f"🔧 Engine Available: {status_data.get('engine_available', False)}")
            print(f"🔗 News Integration: {status_data.get('news_insights_integration', False)}")
            
            # Show capabilities
            capabilities = status_data.get('capabilities', {})
            print(f"\n🎯 Capabilities:")
            for capability, enabled in capabilities.items():
                status_icon = "✅" if enabled else "❌"
                print(f"  {status_icon} {capability.replace('_', ' ').title()}")
            
            # Show data sources
            data_sources = status_data.get('data_sources', {})
            print(f"\n📡 Data Sources:")
            for source, available in data_sources.items():
                status_icon = "✅" if available else "❌"
                print(f"  {status_icon} {source.replace('_', ' ').title()}")
            
            print(f"\n⏰ Last Updated: {status_data.get('last_updated', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Status check failed: {e}")
    
    async def example_6_intent_detection(self):
        """Example 6: Demonstrate news intent detection"""
        print("\n" + "="*60)
        print("🧠 EXAMPLE 6: News Intent Detection")
        print("="*60)
        
        if not self.ai_engine:
            print("❌ AI Engine not available")
            return
        
        # Test various news-related queries
        test_queries = [
            "market news",
            "latest Fed announcement",
            "earnings news for AAPL",
            "what's moving the market today?",
            "news sentiment analysis",
            "breaking financial news",
            "economic calendar events",
            "geopolitical market impact"
        ]
        
        print("Testing news intent detection:")
        
        for query in test_queries:
            try:
                # Analyze intent
                intent_result = await self.ai_engine._analyze_intent(query)
                
                # Check routing decision
                is_simple = self.ai_engine._is_simple_query(query, intent_result)
                
                intent_type = intent_result.get("type", "unknown")
                confidence = intent_result.get("confidence", 0.0)
                routing = "Fast Path" if is_simple else "Full Pipeline"
                
                # Determine if news-related
                is_news = intent_type == "news_insights"
                news_icon = "📰" if is_news else "❓"
                
                print(f"  {news_icon} '{query}'")
                print(f"     Intent: {intent_type} (confidence: {confidence:.2f})")
                print(f"     Routing: {routing}")
                
            except Exception as e:
                print(f"  ❌ '{query}' - Error: {e}")
    
    async def run_all_examples(self):
        """Run all examples in sequence"""
        print("🚀 A.T.L.A.S. News Insights Module - Usage Examples")
        print("="*80)
        
        # Initialize system
        await self.initialize()
        
        # Run examples
        examples = [
            self.example_1_simple_news_query,
            self.example_2_comprehensive_analysis,
            self.example_3_direct_api_usage,
            self.example_4_progress_tracking,
            self.example_5_news_engine_status,
            self.example_6_intent_detection
        ]
        
        for example in examples:
            try:
                await example()
                await asyncio.sleep(1)  # Brief pause between examples
            except Exception as e:
                print(f"❌ Example failed: {e}")
        
        print("\n" + "="*80)
        print("🏁 All examples completed!")
        print("="*80)
        
        # Cleanup
        if self.orchestrator:
            # Cleanup orchestrator resources if needed
            pass

# ============================================================================
# MAIN EXECUTION
# ============================================================================

async def main():
    """Main execution function"""
    examples = NewsInsightsExamples()
    await examples.run_all_examples()

if __name__ == "__main__":
    asyncio.run(main())
