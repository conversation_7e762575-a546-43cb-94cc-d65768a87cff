# A.T.L.A.S. Production Environment Configuration
# PRODUCTION ENVIRONMENT - SECURE CONFIGURATION REQUIRED

# System Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
ENVIRONMENT=production

# CRITICAL: Trading Mode - PA<PERSON><PERSON> TRADING ENFORCED FOR SAFETY
ATLAS_TRADING_MODE=PAPER
PAPER_TRADING=true

# Database Configuration (Production)
DATABASE_URL=postgresql://atlas_user:secure_password@localhost:5432/atlas_production

# API Keys (MUST BE CONFIGURED WITH REAL KEYS)
ALPACA_BASE_URL=https://paper-api.alpaca.markets
ALPACA_API_KEY=your_production_alpaca_api_key_here
ALPACA_SECRET_KEY=your_production_alpaca_secret_key_here

# Financial Modeling Prep API (Production Key Required)
FMP_API_KEY=your_production_fmp_api_key_here

# OpenAI API (Production Key Required)
OPENAI_API_KEY=your_production_openai_api_key_here

# Grok API (Production Key Required)
GROK_API_KEY=your_production_grok_api_key_here

# Email Configuration (Production SMTP)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_production_email_password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/atlas/atlas.log

# Performance Settings (Production Optimized)
MAX_CONCURRENT_REQUESTS=50
CACHE_TTL=60
REQUEST_TIMEOUT=30

# Security Settings (MUST BE CHANGED)
SECRET_KEY=change_this_to_a_secure_random_string_in_production
JWT_SECRET=change_this_to_a_secure_jwt_secret_in_production

# SSL Configuration
SSL_CERT_PATH=/etc/ssl/certs/atlas.crt
SSL_KEY_PATH=/etc/ssl/private/atlas.key

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
PROMETHEUS_ENDPOINT=/metrics

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
