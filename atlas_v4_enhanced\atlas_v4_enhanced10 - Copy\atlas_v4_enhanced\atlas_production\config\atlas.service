[Unit]
Description=A.T.L.A.S. Trading System
After=network.target postgresql.service

[Service]
Type=exec
User=atlas
Group=atlas
WorkingDirectory=/opt/atlas/app
Environment=PATH=/opt/atlas/venv/bin
ExecStart=/opt/atlas/scripts/start_production.sh
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/atlas /opt/atlas/data /opt/atlas/backups

[Install]
WantedBy=multi-user.target
