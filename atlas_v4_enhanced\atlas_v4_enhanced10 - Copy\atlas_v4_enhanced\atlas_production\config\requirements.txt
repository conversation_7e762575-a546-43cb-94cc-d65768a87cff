# A.T.L.A.S AI Trading System - Dependencies
# Production-ready requirements with version pinning

# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Data Models and Validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Async HTTP Client
aiohttp==3.9.1
httpx==0.25.2

# Database
aiosqlite==0.19.0
sqlalchemy==2.0.23

# AI and ML
openai==1.3.7
numpy==1.24.4
pandas==2.1.4
ta==0.10.2

# Optional ML Dependencies (lazy loaded)
tensorflow==2.15.0; platform_machine != "arm64"
torch==2.1.2; platform_machine != "arm64"
scikit-learn==1.3.2

# Vector Database (optional)
chromadb==0.4.18

# Web Search Integration
google-api-python-client==2.95.0
duckduckgo-search==3.8.5

# Advanced ML Libraries (optional with graceful fallbacks)
transformers==4.30.0
scipy==1.11.0

# Options Trading
py_vollib==1.0.1

# Enhanced Features from CHatbotfinal Migration
# Sentiment Analysis and NLP
torch==2.1.2; platform_machine != "arm64"
tokenizers==0.15.0

# Performance Monitoring
psutil==5.9.6
memory-profiler==0.61.0

# Enhanced Technical Analysis
ta-lib==0.4.28; platform_machine != "arm64"
TA-Lib==0.4.28; platform_machine == "arm64"

# Web Search Integration (Enhanced)
beautifulsoup4==4.12.2
lxml==4.9.3

# Async Task Management
celery==5.3.4
redis==5.0.1

# Enhanced Logging and Monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Data Validation and Serialization
marshmallow==3.20.1
jsonschema==4.19.2

# Trading APIs and Market Data
alpaca-trade-api==3.1.1
yfinance==0.2.28

# Environment and Configuration
python-dotenv==1.0.0

# Utilities
python-dateutil==2.8.2
pytz==2023.3
requests==2.31.0

# Development and Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Production Deployment (optional)
gunicorn==21.2.0
docker==6.1.3

# ============================================================================
# A.T.L.A.S. v5.0 ADVANCED AI ENHANCEMENTS
# ============================================================================

# Causal Inference and Reasoning
causalml==0.15.0
dowhy==0.11.1
econml==0.15.0
networkx==3.2.1

# Advanced Multimodal Processing
transformers==4.35.0
torch-audio==2.1.0
opencv-python==4.8.1
pillow==10.1.0
datasets==2.14.6
accelerate==0.24.1

# Explainable AI and Model Interpretability
shap==0.43.0
lime==*******
captum==0.6.0
interpret==0.4.3

# Quantum-Inspired Optimization (optional - graceful fallback)
qutip==4.7.3; platform_machine != "arm64"
qiskit==0.45.0; platform_machine != "arm64"
pennylane==0.33.0; platform_machine != "arm64"

# Privacy-Preserving Machine Learning
opacus==1.4.0
syft==0.8.5; platform_machine != "arm64"

# Bias Detection and Fairness
fairlearn==0.10.0
aif360==0.5.0

# Advanced Computer Vision
torchvision==0.16.0
albumentations==1.3.1
timm==0.9.12

# Natural Language Processing Enhancements
spacy==3.7.2
nltk==3.8.1
sentence-transformers==2.2.2

# Time Series and Financial Analysis
statsmodels==0.14.0
arch==6.2.0
pyfolio==0.9.2

# Cryptocurrency and Blockchain Data
web3==6.11.3
ccxt==4.1.49
python-binance==1.0.19

# International Market Data
yfinance==0.2.28
alpha-vantage==2.3.1
quandl==3.7.0

# Advanced Visualization
plotly==5.17.0
bokeh==3.3.0
seaborn==0.13.0

# Performance Optimization
numba==0.58.1
cython==3.0.5
joblib==1.3.2

# Enhanced Async and Concurrency
asyncpg==0.29.0
aioredis==2.0.1
aiokafka==0.8.11

# Model Serving and MLOps
mlflow==2.8.1
wandb==0.16.0
tensorboard==2.15.1

# Additional Missing Dependencies (from validation)
xgboost==2.0.2
librosa==0.10.1
tweepy==4.14.0
SpeechRecognition==3.10.0
cvxpy==1.4.1