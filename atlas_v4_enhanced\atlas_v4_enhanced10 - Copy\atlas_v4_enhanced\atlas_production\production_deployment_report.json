{"deployment_info": {"timestamp": "2025-07-20T12:52:58.366374", "duration_seconds": 0.035343, "production_directory": "atlas_production", "environment": "production"}, "setup_log": [{"timestamp": "2025-07-20T12:52:58.366900", "step": "Creating production directory structure", "status": "INFO", "details": "Path: atlas_production"}, {"timestamp": "2025-07-20T12:52:58.368609", "step": "Production directory structure created", "status": "INFO", "details": "9 directories"}, {"timestamp": "2025-07-20T12:52:58.368703", "step": "Copying application files to production", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.398284", "step": "Application files copied", "status": "INFO", "details": "70 Python files"}, {"timestamp": "2025-07-20T12:52:58.398451", "step": "Creating production configuration", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.398847", "step": "Production .env file created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.399392", "step": "Production startup script created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.399476", "step": "Creating security configuration", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.400186", "step": "Security configuration files created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.400268", "step": "Creating monitoring configuration", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.400787", "step": "Monitoring configuration created", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.400867", "step": "Creating backup scripts", "status": "INFO", "details": ""}, {"timestamp": "2025-07-20T12:52:58.401634", "step": "Backup scripts created", "status": "INFO", "details": ""}], "summary": {"total_steps": 13, "successful_steps": 13, "errors": 0, "warnings": 0}, "production_checklist": ["1. Configure real API keys in .env.production", "2. Set up SSL certificates", "3. Configure PostgreSQL database", "4. In<PERSON>l and configure <PERSON><PERSON><PERSON>", "5. Set up monitoring (Prometheus/Grafana)", "6. Configure backup schedule", "7. Create atlas user and set permissions", "8. Install systemd service", "9. Configure firewall rules", "10. Test all systems before going live"], "security_requirements": ["Change all default passwords and secrets", "Configure SSL/TLS certificates", "Set up firewall rules", "Enable fail2ban for SSH protection", "Configure log rotation", "Set up intrusion detection", "Regular security updates", "Backup encryption"]}