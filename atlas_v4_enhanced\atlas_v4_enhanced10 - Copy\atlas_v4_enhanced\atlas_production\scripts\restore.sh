#!/bin/bash
# A.T.L.A.S. Production Restore Script

set -e

if [ $# -ne 1 ]; then
    echo "Usage: $0 <backup_file.tar.gz>"
    exit 1
fi

BACKUP_FILE="$1"
RESTORE_DIR="/tmp/atlas_restore_$(date +%s)"

echo "Starting A.T.L.A.S. restore from: $BACKUP_FILE"

# Extract backup
mkdir -p "$RESTORE_DIR"
tar -xzf "$BACKUP_FILE" -C "$RESTORE_DIR"

BACKUP_NAME=$(basename "$BACKUP_FILE" .tar.gz)
BACKUP_PATH="$RESTORE_DIR/$BACKUP_NAME"

# Stop A.T.L.A.S. service
echo "Stopping A.T.L.A.S. service..."
systemctl stop atlas

# Restore database
echo "Restoring database..."
dropdb atlas_production || true
createdb atlas_production
psql atlas_production < "$BACKUP_PATH/database.sql"

# Restore application data
echo "Restoring application data..."
rm -rf /opt/atlas/data/*
tar -xzf "$BACKUP_PATH/app_data.tar.gz" -C /

# Restore configuration (backup current first)
echo "Restoring configuration..."
cp -r /opt/atlas/config /opt/atlas/config.backup.$(date +%s)
cp -r "$BACKUP_PATH/config"/* /opt/atlas/config/

# Start A.T.L.A.S. service
echo "Starting A.T.L.A.S. service..."
systemctl start atlas

# Cleanup
rm -rf "$RESTORE_DIR"

echo "Restore completed successfully"
