"""
A.T.L.A.S. Progress Tracker
Real-time progress tracking and status updates for all system operations
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ProgressStatus(Enum):
    """Progress status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class OperationType(Enum):
    """Operation type enumeration"""
    MARKET_DATA_FETCH = "market_data_fetch"
    TECHNICAL_ANALYSIS = "technical_analysis"
    LEE_METHOD_SCAN = "lee_method_scan"
    OPTIONS_ANALYSIS = "options_analysis"
    OPTIONS_STRATEGY = "options_strategy"
    AI_PROCESSING = "ai_processing"
    MARKET_SCANNING = "market_scanning"
    PATTERN_DETECTION = "pattern_detection"
    RISK_CALCULATION = "risk_calculation"
    TRADING_RECOMMENDATION = "trading_recommendation"
    SYSTEM_SCAN = "system_scan"
    DATA_VALIDATION = "data_validation"
    # News Insights Operations
    NEWS_INGESTION = "news_ingestion"
    NEWS_SENTIMENT_ANALYSIS = "news_sentiment_analysis"
    NEWS_IMPACT_SCORING = "news_impact_scoring"
    NEWS_THEME_CLUSTERING = "news_theme_clustering"
    NEWS_ALERT_GENERATION = "news_alert_generation"
    NEWS_COMPREHENSIVE_ANALYSIS = "news_comprehensive_analysis"

@dataclass
class ProgressStep:
    """Individual progress step"""
    id: str
    name: str
    description: str
    status: ProgressStatus
    progress: float  # 0.0 to 1.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    estimated_duration: Optional[float] = None  # seconds
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class OperationProgress:
    """Complete operation progress tracking"""
    operation_id: str
    operation_type: OperationType
    session_id: str
    title: str
    description: str
    status: ProgressStatus
    overall_progress: float  # 0.0 to 1.0
    steps: List[ProgressStep]
    start_time: datetime
    end_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class AtlasProgressTracker:
    """Real-time progress tracking system for A.T.L.A.S. operations"""
    
    def __init__(self):
        self.active_operations: Dict[str, OperationProgress] = {}
        self.completed_operations: Dict[str, OperationProgress] = {}
        self.progress_callbacks: List[Callable] = []
        self.websocket_connections: Dict[str, Any] = {}  # session_id -> websocket
        self.logger = logging.getLogger(__name__)
        
        # Operation templates with OPTIMIZED durations (4-5 second total max)
        self.operation_templates = {
            OperationType.MARKET_DATA_FETCH: {
                "steps": [
                    {"name": "Connecting to FMP API (25%)", "description": "Establishing connection to Financial Modeling Prep", "duration": 0.5, "progress": 25},
                    {"name": "Fetching real-time quotes (50%)", "description": "Retrieving current market prices", "duration": 0.8, "progress": 50},
                    {"name": "Fetching historical data (75%)", "description": "Loading historical price data", "duration": 1.0, "progress": 75},
                    {"name": "Validating data quality (100%)", "description": "Ensuring data integrity and completeness", "duration": 0.7, "progress": 100}
                ],
                "total_duration": 3.0
            },
            OperationType.TECHNICAL_ANALYSIS: {
                "steps": [
                    {"name": "Calculating MACD indicators", "description": "Computing MACD, signal line, and histogram", "duration": 2.0},
                    {"name": "Computing RSI values", "description": "Calculating Relative Strength Index", "duration": 1.5},
                    {"name": "Analyzing EMA trends", "description": "Computing Exponential Moving Averages", "duration": 1.5},
                    {"name": "Generating technical signals", "description": "Identifying buy/sell signals", "duration": 2.0}
                ],
                "total_duration": 7.0
            },
            OperationType.LEE_METHOD_SCAN: {
                "steps": [
                    {"name": "Initializing Lee Method scanner", "description": "Setting up pattern detection algorithms", "duration": 1.0},
                    {"name": "Analyzing histogram patterns", "description": "Detecting MACD histogram decline/rebound", "duration": 3.0},
                    {"name": "Evaluating EMA alignment", "description": "Checking EMA5 and EMA8 trends", "duration": 2.0},
                    {"name": "Calculating pattern confidence", "description": "Computing signal strength and reliability", "duration": 2.0},
                    {"name": "Generating trading signals", "description": "Creating entry/exit recommendations", "duration": 2.0}
                ],
                "total_duration": 10.0
            },
            OperationType.OPTIONS_ANALYSIS: {
                "steps": [
                    {"name": "Fetching options chain data", "description": "Loading options contracts and prices", "duration": 3.0},
                    {"name": "Calculating Greeks", "description": "Computing Delta, Gamma, Theta, Vega", "duration": 4.0},
                    {"name": "Analyzing implied volatility", "description": "Evaluating IV levels and skew", "duration": 2.0},
                    {"name": "Identifying opportunities", "description": "Finding optimal strike prices and expirations", "duration": 3.0},
                    {"name": "Risk assessment", "description": "Calculating maximum risk and reward", "duration": 2.0}
                ],
                "total_duration": 14.0
            },
            OperationType.AI_PROCESSING: {
                "steps": [
                    {"name": "Intent Detection 🧠 (20%)", "description": "Analyzing query type and determining response format", "duration": 0.4, "progress": 20},
                    {"name": "Engine Selection ⚙️ (40%)", "description": "Selecting appropriate AI engines (Grok/OpenAI) and data sources", "duration": 0.6, "progress": 40},
                    {"name": "Market Data Scanning 📈 (60%)", "description": "Fetching real-time quotes from FMP and Alpaca APIs", "duration": 0.8, "progress": 60},
                    {"name": "AI Analysis Processing 🤖 (80%)", "description": "Processing through Grok AI with OpenAI fallback", "duration": 1.0, "progress": 80},
                    {"name": "Response Formatting ✨ (100%)", "description": "Formatting analysis into 6-point trading format", "duration": 0.7, "progress": 100}
                ],
                "total_duration": 3.5
            },
            OperationType.OPTIONS_STRATEGY: {
                "steps": [
                    {"name": "Options Chain Analysis 📊 (25%)", "description": "Analyzing option chains and liquidity", "duration": 0.6, "progress": 25},
                    {"name": "Black-Scholes Calculation 💰 (50%)", "description": "Computing option valuations and Greeks", "duration": 0.8, "progress": 50},
                    {"name": "Strategy Optimization ⚙️ (75%)", "description": "Optimizing strike prices and expiration dates", "duration": 0.9, "progress": 75},
                    {"name": "Risk Assessment 🛡️ (100%)", "description": "Calculating risk/reward and probability analysis", "duration": 0.7, "progress": 100}
                ],
                "total_duration": 3.0
            },
            OperationType.MARKET_SCANNING: {
                "steps": [
                    {"name": "Fed Announcements 🏛️ (20%)", "description": "Checking Federal Reserve statements and policy updates", "duration": 0.6, "progress": 20},
                    {"name": "Reuters API 📰 (40%)", "description": "Fetching breaking news from Reuters financial feeds", "duration": 0.7, "progress": 40},
                    {"name": "Bloomberg Feeds 📊 (60%)", "description": "Scanning Bloomberg terminal data and market alerts", "duration": 0.8, "progress": 60},
                    {"name": "Market Sentiment 📈 (80%)", "description": "Processing social media and news sentiment data", "duration": 0.6, "progress": 80},
                    {"name": "Analysis Complete ✅ (100%)", "description": "Finalizing market intelligence report", "duration": 0.3, "progress": 100}
                ],
                "total_duration": 3.0
            },
            # News Insights Operation Templates
            OperationType.NEWS_INGESTION: {
                "steps": [
                    {"name": "Connecting to News APIs 🔗 (25%)", "description": "Establishing connections to FMP, Alpha Vantage, and social media APIs", "duration": 0.5, "progress": 25},
                    {"name": "Fetching Latest Headlines 📰 (50%)", "description": "Retrieving breaking news and market-moving events", "duration": 0.8, "progress": 50},
                    {"name": "Processing SEC Filings 📋 (75%)", "description": "Scanning EDGAR database for regulatory updates", "duration": 0.7, "progress": 75},
                    {"name": "Data Validation ✅ (100%)", "description": "Ensuring data quality and removing duplicates", "duration": 0.4, "progress": 100}
                ],
                "total_duration": 2.4
            },
            OperationType.NEWS_SENTIMENT_ANALYSIS: {
                "steps": [
                    {"name": "Text Preprocessing 🔤 (20%)", "description": "Cleaning and tokenizing news content", "duration": 0.3, "progress": 20},
                    {"name": "DistilBERT Analysis 🤖 (50%)", "description": "Running sentiment analysis with fine-tuned financial model", "duration": 0.8, "progress": 50},
                    {"name": "Confidence Scoring 📊 (75%)", "description": "Computing sentiment confidence and market direction", "duration": 0.5, "progress": 75},
                    {"name": "Sentiment Aggregation ⚖️ (100%)", "description": "Aggregating multi-source sentiment scores", "duration": 0.4, "progress": 100}
                ],
                "total_duration": 2.0
            },
            OperationType.NEWS_IMPACT_SCORING: {
                "steps": [
                    {"name": "Source Credibility 🏆 (25%)", "description": "Weighting by source reliability and historical accuracy", "duration": 0.4, "progress": 25},
                    {"name": "Market Correlation 📈 (50%)", "description": "Analyzing historical market reaction patterns", "duration": 0.6, "progress": 50},
                    {"name": "Volatility Prediction 📊 (75%)", "description": "Correlating with VIX movements and sector volatility", "duration": 0.7, "progress": 75},
                    {"name": "Impact Score Generation 🎯 (100%)", "description": "Computing final market impact probability", "duration": 0.3, "progress": 100}
                ],
                "total_duration": 2.0
            },
            OperationType.NEWS_THEME_CLUSTERING: {
                "steps": [
                    {"name": "BERT Embeddings 🧠 (30%)", "description": "Generating semantic embeddings for news content", "duration": 0.6, "progress": 30},
                    {"name": "K-means Clustering 🔍 (60%)", "description": "Identifying emerging market narratives and themes", "duration": 0.8, "progress": 60},
                    {"name": "Theme Classification 🏷️ (85%)", "description": "Auto-tagging with A.T.L.A.S. categories", "duration": 0.5, "progress": 85},
                    {"name": "Trend Analysis 📈 (100%)", "description": "Detecting narrative shifts and momentum", "duration": 0.4, "progress": 100}
                ],
                "total_duration": 2.3
            },
            OperationType.NEWS_ALERT_GENERATION: {
                "steps": [
                    {"name": "Threshold Monitoring 🚨 (25%)", "description": "Checking sentiment and volume thresholds", "duration": 0.2, "progress": 25},
                    {"name": "Alert Prioritization 📋 (50%)", "description": "Ranking alerts by market impact and urgency", "duration": 0.4, "progress": 50},
                    {"name": "WebSocket Broadcasting 📡 (75%)", "description": "Sending real-time alerts to connected clients", "duration": 0.3, "progress": 75},
                    {"name": "Alert Delivery ✅ (100%)", "description": "Confirming delivery and updating alert status", "duration": 0.2, "progress": 100}
                ],
                "total_duration": 1.1
            },
            OperationType.NEWS_COMPREHENSIVE_ANALYSIS: {
                "steps": [
                    {"name": "Multi-source Ingestion 📥 (15%)", "description": "Gathering news from all configured sources", "duration": 0.8, "progress": 15},
                    {"name": "Sentiment Processing 🎭 (35%)", "description": "Analyzing sentiment across all sources", "duration": 1.2, "progress": 35},
                    {"name": "Impact Correlation 🔗 (55%)", "description": "Correlating news impact with market movements", "duration": 1.0, "progress": 55},
                    {"name": "Theme Identification 🎯 (75%)", "description": "Clustering themes and identifying trends", "duration": 0.9, "progress": 75},
                    {"name": "Alert Generation 🚨 (90%)", "description": "Generating prioritized market alerts", "duration": 0.6, "progress": 90},
                    {"name": "Final Report 📊 (100%)", "description": "Compiling comprehensive news insights report", "duration": 0.5, "progress": 100}
                ],
                "total_duration": 5.0
            }
        }
    
    def create_operation(self, operation_type: OperationType, session_id: str, 
                        title: str, description: str, metadata: Dict[str, Any] = None) -> str:
        """Create a new tracked operation"""
        operation_id = str(uuid.uuid4())
        
        # Get template for this operation type
        template = self.operation_templates.get(operation_type, {
            "steps": [{"name": "Processing", "description": "Operation in progress", "duration": 5.0}],
            "total_duration": 5.0
        })
        
        # Create progress steps
        steps = []
        for i, step_template in enumerate(template["steps"]):
            step = ProgressStep(
                id=f"{operation_id}_step_{i}",
                name=step_template["name"],
                description=step_template["description"],
                status=ProgressStatus.PENDING,
                progress=0.0,
                estimated_duration=step_template["duration"]
            )
            steps.append(step)
        
        # Create operation progress
        operation = OperationProgress(
            operation_id=operation_id,
            operation_type=operation_type,
            session_id=session_id,
            title=title,
            description=description,
            status=ProgressStatus.PENDING,
            overall_progress=0.0,
            steps=steps,
            start_time=datetime.now(),
            estimated_completion=datetime.now() + timedelta(seconds=template["total_duration"]),
            metadata=metadata or {}
        )
        
        self.active_operations[operation_id] = operation
        self.logger.info(f"[PROGRESS] Created operation {operation_id}: {title}")
        
        # Notify WebSocket connections
        asyncio.create_task(self._notify_progress_update(operation))
        
        return operation_id
    
    async def start_step(self, operation_id: str, step_index: int) -> bool:
        """Start a specific step in an operation"""
        if operation_id not in self.active_operations:
            return False
        
        operation = self.active_operations[operation_id]
        if step_index >= len(operation.steps):
            return False
        
        step = operation.steps[step_index]
        step.status = ProgressStatus.IN_PROGRESS
        step.start_time = datetime.now()
        step.progress = 0.0
        
        # Update operation status
        operation.status = ProgressStatus.IN_PROGRESS
        
        self.logger.info(f"[PROGRESS] Started step {step_index} for operation {operation_id}: {step.name}")
        
        # Notify WebSocket connections
        await self._notify_progress_update(operation)
        
        return True
    
    async def update_step_progress(self, operation_id: str, step_index: int, 
                                 progress: float, message: str = None) -> bool:
        """Update progress for a specific step"""
        if operation_id not in self.active_operations:
            return False
        
        operation = self.active_operations[operation_id]
        if step_index >= len(operation.steps):
            return False
        
        step = operation.steps[step_index]
        step.progress = max(0.0, min(1.0, progress))
        
        if message:
            step.description = message
        
        # Update overall progress
        total_progress = sum(s.progress for s in operation.steps) / len(operation.steps)
        operation.overall_progress = total_progress
        
        # Notify WebSocket connections
        await self._notify_progress_update(operation)
        
        return True
    
    async def complete_step(self, operation_id: str, step_index: int, 
                          success: bool = True, error_message: str = None) -> bool:
        """Complete a specific step"""
        if operation_id not in self.active_operations:
            return False
        
        operation = self.active_operations[operation_id]
        if step_index >= len(operation.steps):
            return False
        
        step = operation.steps[step_index]
        step.status = ProgressStatus.COMPLETED if success else ProgressStatus.FAILED
        step.progress = 1.0 if success else step.progress
        step.end_time = datetime.now()
        
        if error_message:
            step.error_message = error_message
        
        # Update overall progress
        total_progress = sum(s.progress for s in operation.steps) / len(operation.steps)
        operation.overall_progress = total_progress
        
        self.logger.info(f"[PROGRESS] Completed step {step_index} for operation {operation_id}: {step.name}")
        
        # Check if all steps are complete
        if all(s.status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED] for s in operation.steps):
            await self.complete_operation(operation_id, success)
        
        # Notify WebSocket connections
        await self._notify_progress_update(operation)
        
        return True
    
    async def complete_operation(self, operation_id: str, success: bool = True) -> bool:
        """Complete an entire operation"""
        if operation_id not in self.active_operations:
            return False
        
        operation = self.active_operations[operation_id]
        operation.status = ProgressStatus.COMPLETED if success else ProgressStatus.FAILED
        operation.end_time = datetime.now()
        operation.overall_progress = 1.0 if success else operation.overall_progress
        
        # Move to completed operations
        self.completed_operations[operation_id] = operation
        del self.active_operations[operation_id]
        
        self.logger.info(f"[PROGRESS] Completed operation {operation_id}: {operation.title}")
        
        # Notify WebSocket connections
        await self._notify_progress_update(operation)
        
        return True
    
    async def _notify_progress_update(self, operation: OperationProgress):
        """Notify WebSocket connections of progress updates"""
        if operation.session_id in self.websocket_connections:
            try:
                websocket = self.websocket_connections[operation.session_id]

                # Get current active step
                current_step = None
                current_step_index = -1

                # Convert operation to dict to access step data
                operation_dict = asdict(operation)

                for i, step in enumerate(operation_dict.get('steps', [])):
                    if 'IN_PROGRESS' in str(step.get('status', '')):
                        current_step = step
                        current_step_index = i
                        break

                # If no step is in progress, find the last completed step
                if current_step is None:
                    for i, step in enumerate(operation_dict.get('steps', [])):
                        if 'COMPLETED' in str(step.get('status', '')):
                            current_step = step
                            current_step_index = i

                # If still no step found, use the first step
                if current_step is None and operation_dict.get('steps'):
                    current_step = operation_dict['steps'][0]
                    current_step_index = 0

                # Format data for frontend compatibility
                stage_name = current_step.get('name', operation_dict.get('title', 'Processing')) if current_step else operation_dict.get('title', 'Processing')
                percentage = int(operation.overall_progress * 100)
                message = current_step.get('description', operation_dict.get('description', '')) if current_step else operation_dict.get('description', '')
                status = str(operation.status).replace('ProgressStatus.', '').lower() if hasattr(operation, 'status') else 'in_progress'

                update_data = {
                    "type": "progress_update",
                    "data": {
                        "stage": stage_name,
                        "percentage": percentage,
                        "message": message,
                        "status": status,
                        "step_index": current_step_index,
                        "total_steps": len(operation.steps)
                    },
                    "operation": asdict(operation),  # Keep full operation for compatibility
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(update_data, default=str))
                self.logger.debug(f"[PROGRESS] Sent update: {stage_name} - {percentage}%")
            except Exception as e:
                self.logger.error(f"Failed to send progress update: {e}")
    
    def get_operation_status(self, operation_id: str) -> Optional[OperationProgress]:
        """Get current status of an operation"""
        if operation_id in self.active_operations:
            return self.active_operations[operation_id]
        elif operation_id in self.completed_operations:
            return self.completed_operations[operation_id]
        return None
    
    def get_session_operations(self, session_id: str) -> List[OperationProgress]:
        """Get all operations for a session"""
        operations = []
        
        # Active operations
        for op in self.active_operations.values():
            if op.session_id == session_id:
                operations.append(op)
        
        # Recent completed operations (last 10)
        completed_for_session = [op for op in self.completed_operations.values() 
                               if op.session_id == session_id]
        completed_for_session.sort(key=lambda x: x.end_time or x.start_time, reverse=True)
        operations.extend(completed_for_session[:10])
        
        return operations
    
    def register_websocket(self, session_id: str, websocket: Any):
        """Register a WebSocket connection for progress updates"""
        self.websocket_connections[session_id] = websocket
        self.logger.info(f"[PROGRESS] Registered WebSocket for session {session_id}")
    
    def unregister_websocket(self, session_id: str):
        """Unregister a WebSocket connection"""
        if session_id in self.websocket_connections:
            del self.websocket_connections[session_id]
            self.logger.info(f"[PROGRESS] Unregistered WebSocket for session {session_id}")

# Global progress tracker instance
progress_tracker = AtlasProgressTracker()
