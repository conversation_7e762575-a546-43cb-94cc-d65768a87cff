"""
A.T.L.A.S Security - Consolidated Security and Compliance Management
Combines Security Manager, Compliance Engine, and Ultimate 100% Enforcer
"""

import asyncio
import logging
import json
import sys
import os
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

logger = logging.getLogger(__name__)


# ============================================================================
# SECURITY DATA STRUCTURES
# ============================================================================

class SecurityLevel(Enum):
    """Security levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ComplianceStatus(Enum):
    """Compliance status"""
    COMPLIANT = "compliant"
    WARNING = "warning"
    VIOLATION = "violation"
    CRITICAL = "critical"


@dataclass
class SecurityEvent:
    """Security event data structure"""
    event_id: str
    event_type: str
    severity: SecurityLevel
    description: str
    timestamp: datetime
    source: str
    metadata: Dict[str, Any]


@dataclass
class ComplianceRule:
    """Compliance rule definition"""
    rule_id: str
    name: str
    description: str
    category: str
    severity: SecurityLevel
    check_function: str
    parameters: Dict[str, Any]


# ============================================================================
# SECURITY MANAGER
# ============================================================================

class AtlasSecurityManager:
    """Comprehensive security management system"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.security_events = []
        self.api_keys = {}
        self.rate_limits = {}
        self.access_logs = []
        self.security_config = {}
        
        logger.info("[SHIELD] Security Manager initialized - protection: active")

    async def initialize(self):
        """Initialize security manager"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load security configuration
            await self._load_security_config()
            
            # Initialize rate limiting
            await self._initialize_rate_limiting()
            
            # Setup security monitoring
            await self._setup_security_monitoring()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Security Manager initialization completed")
            
        except Exception as e:
            logger.error(f"Security Manager initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_security_config(self):
        """Load security configuration"""
        try:
            self.security_config = {
                'api_key_rotation_days': 90,
                'max_failed_attempts': 5,
                'session_timeout_minutes': 30,
                'rate_limit_requests_per_minute': 100,
                'encryption_algorithm': 'AES-256',
                'audit_log_retention_days': 365
            }
            
            logger.info("[CONFIG] Security configuration loaded")
            
        except Exception as e:
            logger.error(f"Security config loading failed: {e}")
            raise

    async def _initialize_rate_limiting(self):
        """Initialize rate limiting system"""
        try:
            self.rate_limits = {
                'api_requests': {
                    'limit': self.security_config['rate_limit_requests_per_minute'],
                    'window': 60,  # seconds
                    'requests': {}
                },
                'login_attempts': {
                    'limit': self.security_config['max_failed_attempts'],
                    'window': 300,  # 5 minutes
                    'attempts': {}
                }
            }
            
            logger.info("[RATE] Rate limiting initialized")
            
        except Exception as e:
            logger.error(f"Rate limiting initialization failed: {e}")
            raise

    async def _setup_security_monitoring(self):
        """Setup security monitoring"""
        try:
            # Initialize security event monitoring
            self.monitoring_enabled = True
            self.alert_thresholds = {
                'failed_logins': 3,
                'api_errors': 10,
                'unusual_activity': 5
            }
            
            logger.info("[MONITOR] Security monitoring active")
            
        except Exception as e:
            logger.error(f"Security monitoring setup failed: {e}")
            raise

    async def validate_api_key(self, service: str, api_key: str) -> Dict[str, Any]:
        """Validate API key for service"""
        try:
            # Basic validation
            if not api_key or len(api_key) < 10:
                await self._log_security_event(
                    "invalid_api_key",
                    SecurityLevel.MEDIUM,
                    f"Invalid API key format for {service}",
                    {"service": service}
                )
                return {"valid": False, "reason": "Invalid format"}
            
            # Check if key is known
            if service in self.api_keys:
                stored_hash = self.api_keys[service]
                provided_hash = hashlib.sha256(api_key.encode()).hexdigest()
                
                if stored_hash == provided_hash:
                    return {"valid": True, "service": service}
                else:
                    await self._log_security_event(
                        "api_key_mismatch",
                        SecurityLevel.HIGH,
                        f"API key mismatch for {service}",
                        {"service": service}
                    )
                    return {"valid": False, "reason": "Key mismatch"}
            
            # Store new key (hash only)
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            self.api_keys[service] = key_hash
            
            await self._log_security_event(
                "api_key_registered",
                SecurityLevel.LOW,
                f"New API key registered for {service}",
                {"service": service}
            )
            
            return {"valid": True, "service": service, "status": "registered"}
            
        except Exception as e:
            logger.error(f"API key validation failed: {e}")
            return {"valid": False, "reason": "Validation error"}

    async def check_rate_limit(self, identifier: str, limit_type: str = 'api_requests') -> Dict[str, Any]:
        """Check rate limit for identifier"""
        try:
            if limit_type not in self.rate_limits:
                return {"allowed": True, "reason": "Unknown limit type"}
            
            limit_config = self.rate_limits[limit_type]
            current_time = datetime.now()
            
            # Clean old entries
            cutoff_time = current_time - timedelta(seconds=limit_config['window'])
            if identifier in limit_config['requests']:
                limit_config['requests'][identifier] = [
                    req_time for req_time in limit_config['requests'][identifier]
                    if req_time > cutoff_time
                ]
            
            # Check current count
            current_count = len(limit_config['requests'].get(identifier, []))
            
            if current_count >= limit_config['limit']:
                await self._log_security_event(
                    "rate_limit_exceeded",
                    SecurityLevel.MEDIUM,
                    f"Rate limit exceeded for {identifier}",
                    {"identifier": identifier, "limit_type": limit_type, "count": current_count}
                )
                return {
                    "allowed": False,
                    "reason": "Rate limit exceeded",
                    "current_count": current_count,
                    "limit": limit_config['limit'],
                    "reset_time": (current_time + timedelta(seconds=limit_config['window'])).isoformat()
                }
            
            # Record request
            if identifier not in limit_config['requests']:
                limit_config['requests'][identifier] = []
            limit_config['requests'][identifier].append(current_time)
            
            return {
                "allowed": True,
                "current_count": current_count + 1,
                "limit": limit_config['limit'],
                "remaining": limit_config['limit'] - current_count - 1
            }
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            return {"allowed": True, "reason": "Check failed"}

    async def _log_security_event(self, event_type: str, severity: SecurityLevel, 
                                description: str, metadata: Dict[str, Any] = None):
        """Log security event"""
        try:
            event = SecurityEvent(
                event_id=secrets.token_hex(8),
                event_type=event_type,
                severity=severity,
                description=description,
                timestamp=datetime.now(),
                source="security_manager",
                metadata=metadata or {}
            )
            
            self.security_events.append(event)
            
            # Keep only recent events
            if len(self.security_events) > 1000:
                self.security_events = self.security_events[-1000:]
            
            logger.info(f"[SECURITY] {severity.value.upper()}: {description}")
            
        except Exception as e:
            logger.error(f"Security event logging failed: {e}")

    def get_security_status(self) -> Dict[str, Any]:
        """Get security status summary"""
        try:
            recent_events = [
                event for event in self.security_events
                if (datetime.now() - event.timestamp).days < 1
            ]
            
            severity_counts = {}
            for event in recent_events:
                severity_counts[event.severity.value] = severity_counts.get(event.severity.value, 0) + 1
            
            return {
                "status": self.status.value,
                "monitoring_enabled": self.monitoring_enabled,
                "total_events_24h": len(recent_events),
                "events_by_severity": severity_counts,
                "registered_services": len(self.api_keys),
                "rate_limit_status": {
                    limit_type: {
                        "active_identifiers": len(config['requests']),
                        "limit": config['limit'],
                        "window_seconds": config['window']
                    }
                    for limit_type, config in self.rate_limits.items()
                }
            }
            
        except Exception as e:
            logger.error(f"Security status check failed: {e}")
            return {"status": "error", "error": str(e)}


# ============================================================================
# COMPLIANCE ENGINE
# ============================================================================

class AtlasComplianceEngine:
    """Regulatory compliance and audit engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.compliance_rules = {}
        self.compliance_logs = []
        self.audit_trail = []
        
        logger.info("[COMPLIANCE] Compliance Engine initialized - audit: active")

    async def initialize(self):
        """Initialize compliance engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load compliance rules
            await self._load_compliance_rules()
            
            # Initialize audit trail
            await self._initialize_audit_trail()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Compliance Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Compliance Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_compliance_rules(self):
        """Load compliance rules"""
        try:
            self.compliance_rules = {
                'data_retention': ComplianceRule(
                    rule_id="DR001",
                    name="Data Retention Policy",
                    description="Ensure data is retained according to policy",
                    category="data_governance",
                    severity=SecurityLevel.MEDIUM,
                    check_function="check_data_retention",
                    parameters={"retention_days": 365}
                ),
                'api_logging': ComplianceRule(
                    rule_id="AL001",
                    name="API Access Logging",
                    description="All API access must be logged",
                    category="access_control",
                    severity=SecurityLevel.HIGH,
                    check_function="check_api_logging",
                    parameters={"log_all_requests": True}
                ),
                'encryption_standards': ComplianceRule(
                    rule_id="ES001",
                    name="Encryption Standards",
                    description="Sensitive data must be encrypted",
                    category="data_protection",
                    severity=SecurityLevel.CRITICAL,
                    check_function="check_encryption",
                    parameters={"min_key_length": 256}
                )
            }
            
            logger.info(f"[RULES] {len(self.compliance_rules)} compliance rules loaded")
            
        except Exception as e:
            logger.error(f"Compliance rules loading failed: {e}")
            raise

    async def _initialize_audit_trail(self):
        """Initialize audit trail system"""
        try:
            self.audit_config = {
                'log_all_actions': True,
                'retention_days': 365,
                'encryption_enabled': True,
                'real_time_monitoring': True
            }
            
            logger.info("[AUDIT] Audit trail system initialized")
            
        except Exception as e:
            logger.error(f"Audit trail initialization failed: {e}")
            raise

    async def log_compliance_event(self, event_type: str, description: str, 
                                 user_id: str = None, metadata: Dict[str, Any] = None):
        """Log compliance event"""
        try:
            event = {
                'event_id': secrets.token_hex(8),
                'event_type': event_type,
                'description': description,
                'user_id': user_id,
                'timestamp': datetime.now().isoformat(),
                'metadata': metadata or {}
            }
            
            self.compliance_logs.append(event)
            
            # Keep only recent logs
            if len(self.compliance_logs) > 10000:
                self.compliance_logs = self.compliance_logs[-10000:]
            
            logger.info(f"[COMPLIANCE] Event logged: {event_type}")
            
        except Exception as e:
            logger.error(f"Compliance event logging failed: {e}")

    async def check_compliance(self, rule_id: str = None) -> Dict[str, Any]:
        """Check compliance against rules"""
        try:
            if rule_id:
                # Check specific rule
                if rule_id not in self.compliance_rules:
                    return {"error": f"Unknown rule: {rule_id}"}
                
                rule = self.compliance_rules[rule_id]
                result = await self._check_rule(rule)
                return {"rule_id": rule_id, "result": result}
            else:
                # Check all rules
                results = {}
                for rule_id, rule in self.compliance_rules.items():
                    results[rule_id] = await self._check_rule(rule)
                
                # Calculate overall compliance
                total_rules = len(results)
                compliant_rules = sum(1 for result in results.values() if result['status'] == ComplianceStatus.COMPLIANT.value)
                compliance_percentage = (compliant_rules / total_rules) * 100 if total_rules > 0 else 100
                
                return {
                    "overall_compliance": compliance_percentage,
                    "total_rules": total_rules,
                    "compliant_rules": compliant_rules,
                    "rule_results": results,
                    "status": "compliant" if compliance_percentage >= 90 else "warning" if compliance_percentage >= 70 else "violation"
                }
                
        except Exception as e:
            logger.error(f"Compliance check failed: {e}")
            return {"error": str(e)}

    async def _check_rule(self, rule: ComplianceRule) -> Dict[str, Any]:
        """Check individual compliance rule"""
        try:
            # Simulate rule checking
            # In production, this would implement actual compliance checks
            
            if rule.check_function == "check_data_retention":
                # Check data retention compliance
                status = ComplianceStatus.COMPLIANT
                message = "Data retention policy is being followed"
                
            elif rule.check_function == "check_api_logging":
                # Check API logging compliance
                status = ComplianceStatus.COMPLIANT
                message = "All API requests are being logged"
                
            elif rule.check_function == "check_encryption":
                # Check encryption compliance
                status = ComplianceStatus.COMPLIANT
                message = "Encryption standards are met"
                
            else:
                status = ComplianceStatus.WARNING
                message = f"Unknown check function: {rule.check_function}"
            
            return {
                "rule_name": rule.name,
                "status": status.value,
                "message": message,
                "severity": rule.severity.value,
                "checked_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Rule check failed for {rule.rule_id}: {e}")
            return {
                "rule_name": rule.name,
                "status": ComplianceStatus.VIOLATION.value,
                "message": f"Check failed: {str(e)}",
                "severity": rule.severity.value,
                "checked_at": datetime.now().isoformat()
            }


# ============================================================================
# ULTIMATE 100% ENFORCER
# ============================================================================

class AtlasUltimate100PercentEnforcer:
    """Ultimate enforcement system for critical security policies"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.enforcement_rules = {}
        self.violations = []
        self.enforcement_enabled = True
        
        logger.info("[ENFORCER] Ultimate 100% Enforcer initialized - enforcement: active")

    async def initialize(self):
        """Initialize enforcement system"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load enforcement rules
            await self._load_enforcement_rules()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Ultimate 100% Enforcer ready")
            
        except Exception as e:
            logger.error(f"Ultimate Enforcer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_enforcement_rules(self):
        """Load critical enforcement rules"""
        try:
            self.enforcement_rules = {
                'max_position_size': {
                    'description': 'Maximum position size cannot exceed 2% of portfolio',
                    'threshold': 0.02,
                    'action': 'block_trade',
                    'severity': 'critical'
                },
                'daily_loss_limit': {
                    'description': 'Daily loss cannot exceed 5% of portfolio',
                    'threshold': 0.05,
                    'action': 'halt_trading',
                    'severity': 'critical'
                },
                'api_rate_limit': {
                    'description': 'API requests cannot exceed rate limits',
                    'threshold': 100,  # requests per minute
                    'action': 'throttle_requests',
                    'severity': 'high'
                }
            }
            
            logger.info(f"[RULES] {len(self.enforcement_rules)} enforcement rules loaded")
            
        except Exception as e:
            logger.error(f"Enforcement rules loading failed: {e}")
            raise

    async def enforce_rule(self, rule_name: str, current_value: float, 
                         context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Enforce specific rule"""
        try:
            if not self.enforcement_enabled:
                return {"enforced": False, "reason": "Enforcement disabled"}
            
            if rule_name not in self.enforcement_rules:
                return {"enforced": False, "reason": f"Unknown rule: {rule_name}"}
            
            rule = self.enforcement_rules[rule_name]
            threshold = rule['threshold']
            
            # Check if rule is violated
            if current_value > threshold:
                violation = {
                    'rule_name': rule_name,
                    'description': rule['description'],
                    'current_value': current_value,
                    'threshold': threshold,
                    'action': rule['action'],
                    'severity': rule['severity'],
                    'timestamp': datetime.now().isoformat(),
                    'context': context or {}
                }
                
                self.violations.append(violation)
                
                # Take enforcement action
                action_result = await self._take_enforcement_action(rule['action'], violation)
                
                logger.warning(f"[VIOLATION] Rule {rule_name} violated: {current_value} > {threshold}")
                
                return {
                    "enforced": True,
                    "violated": True,
                    "violation": violation,
                    "action_taken": action_result
                }
            else:
                return {
                    "enforced": True,
                    "violated": False,
                    "current_value": current_value,
                    "threshold": threshold,
                    "status": "compliant"
                }
                
        except Exception as e:
            logger.error(f"Rule enforcement failed for {rule_name}: {e}")
            return {"enforced": False, "error": str(e)}

    async def _take_enforcement_action(self, action: str, violation: Dict[str, Any]) -> Dict[str, Any]:
        """Take enforcement action"""
        try:
            if action == 'block_trade':
                return {
                    "action": "block_trade",
                    "description": "Trade blocked due to position size violation",
                    "status": "blocked"
                }
            elif action == 'halt_trading':
                return {
                    "action": "halt_trading",
                    "description": "Trading halted due to daily loss limit",
                    "status": "halted"
                }
            elif action == 'throttle_requests':
                return {
                    "action": "throttle_requests",
                    "description": "API requests throttled due to rate limit",
                    "status": "throttled"
                }
            else:
                return {
                    "action": action,
                    "description": f"Unknown action: {action}",
                    "status": "unknown"
                }
                
        except Exception as e:
            logger.error(f"Enforcement action failed: {e}")
            return {"action": action, "status": "failed", "error": str(e)}

    def get_enforcement_status(self) -> Dict[str, Any]:
        """Get enforcement status"""
        return {
            "enforcement_enabled": self.enforcement_enabled,
            "total_rules": len(self.enforcement_rules),
            "total_violations": len(self.violations),
            "recent_violations": [
                v for v in self.violations
                if (datetime.now() - datetime.fromisoformat(v['timestamp'])).days < 1
            ]
        }


# ============================================================================
# SECURITY ORCHESTRATOR
# ============================================================================

class AtlasSecurityOrchestrator:
    """Main security orchestrator combining all security components"""
    
    def __init__(self):
        self.security_manager = AtlasSecurityManager()
        self.compliance_engine = AtlasComplianceEngine()
        self.ultimate_enforcer = AtlasUltimate100PercentEnforcer()
        self.status = EngineStatus.INITIALIZING
        
        logger.info("[ORCHESTRATOR] Security Orchestrator initialized")

    async def initialize(self):
        """Initialize all security components"""
        try:
            await self.security_manager.initialize()
            await self.compliance_engine.initialize()
            await self.ultimate_enforcer.initialize()
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Security Orchestrator fully initialized")
            
        except Exception as e:
            logger.error(f"Security Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def comprehensive_security_check(self) -> Dict[str, Any]:
        """Perform comprehensive security check"""
        try:
            # Run all security checks
            security_status = self.security_manager.get_security_status()
            compliance_check = await self.compliance_engine.check_compliance()
            enforcement_status = self.ultimate_enforcer.get_enforcement_status()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "security_status": security_status,
                "compliance_check": compliance_check,
                "enforcement_status": enforcement_status,
                "overall_security_level": self._calculate_overall_security_level(
                    security_status, compliance_check, enforcement_status
                )
            }
            
        except Exception as e:
            logger.error(f"Comprehensive security check failed: {e}")
            return {"error": str(e)}

    def _calculate_overall_security_level(self, security_status: Dict[str, Any], 
                                        compliance_check: Dict[str, Any], 
                                        enforcement_status: Dict[str, Any]) -> str:
        """Calculate overall security level"""
        try:
            # Simple scoring system
            score = 100
            
            # Deduct for security events
            if security_status.get('total_events_24h', 0) > 10:
                score -= 20
            
            # Deduct for compliance issues
            compliance_percentage = compliance_check.get('overall_compliance', 100)
            if compliance_percentage < 90:
                score -= (100 - compliance_percentage)
            
            # Deduct for violations
            if enforcement_status.get('total_violations', 0) > 0:
                score -= 30
            
            if score >= 90:
                return "excellent"
            elif score >= 70:
                return "good"
            elif score >= 50:
                return "fair"
            else:
                return "poor"
                
        except Exception as e:
            logger.error(f"Security level calculation failed: {e}")
            return "unknown"


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasSecurityManager",
    "AtlasComplianceEngine",
    "AtlasUltimate100PercentEnforcer",
    "AtlasSecurityOrchestrator",
    "SecurityEvent",
    "ComplianceRule",
    "SecurityLevel",
    "ComplianceStatus"
]
