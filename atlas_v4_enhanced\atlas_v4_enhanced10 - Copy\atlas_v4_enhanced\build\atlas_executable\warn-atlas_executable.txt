
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), eventlet.patcher (delayed, conditional, optional)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), asyncio.base_events (top-level), http.client (top-level), asyncio.coroutines (top-level), pydantic_core._pydantic_core (top-level), typing_extensions (top-level), pydantic_core.core_schema (top-level), typing_inspection.introspection (top-level), typing_inspection.typing_objects (top-level), pydantic._internal._typing_extra (top-level), pydantic._internal._namespace_utils (top-level), pydantic._internal._utils (top-level), pydantic.fields (top-level), pydantic.types (top-level), pydantic._internal._fields (top-level), pydantic._internal._generics (top-level), pydantic._internal._core_utils (top-level), pydantic._internal._generate_schema (top-level), pydantic.json_schema (top-level), pydantic._internal._decorators (top-level), pydantic._internal._mock_val_ser (top-level), pydantic.plugin._schema_validator (top-level), pydantic.plugin._loader (top-level), pydantic.main (top-level), pydantic.type_adapter (top-level), pydantic._internal._discriminated_union (top-level), pydantic._internal._known_annotated_metadata (top-level), pydantic._internal._validators (top-level), pydantic._internal._serializers (top-level), pydantic.v1.typing (top-level), pydantic.v1.fields (top-level), Cython.Build.Dependencies (top-level), pkg_resources (top-level), setuptools (top-level), setuptools._distutils.filelist (top-level), setuptools._distutils.util (top-level), setuptools._vendor.jaraco.functools (top-level), setuptools._vendor.more_itertools.more (top-level), setuptools._vendor.more_itertools.recipes (top-level), setuptools._distutils._modified (top-level), setuptools._distutils.compat (top-level), setuptools._distutils.spawn (top-level), setuptools._distutils.compilers.C.base (top-level), setuptools._distutils.fancy_getopt (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), importlib_metadata (top-level), importlib_metadata._meta (top-level), setuptools._distutils.command.bdist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), configparser (top-level), setuptools._distutils.extension (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools._distutils.command.build (top-level), setuptools._distutils.command.sdist (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), wheel.cli.convert (top-level), wheel.cli.tags (top-level), platformdirs.api (conditional), platformdirs.windows (conditional), platformdirs.unix (conditional), xml.etree.ElementTree (top-level), setuptools._distutils.command.build_ext (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), setuptools._distutils.compilers.C.msvc (top-level), setuptools.command.build_ext (top-level), numpy.lib._npyio_impl (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), yaml.constructor (top-level), numpy.random.bit_generator (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level), IPython.external.pickleshare (optional), IPython.core.ultratb (top-level), IPython.core.doctb (top-level), trio._core._entry_queue (top-level), attr._compat (top-level), attr._make (top-level), trio._util (top-level), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional), trio._socket (conditional), trio._core._parking_lot (conditional), trio._threads (conditional), trio._core._traps (conditional), trio._subprocess (conditional), trio._deprecate (conditional), trio._core._asyncgens (conditional), trio._core._instrumentation (conditional), trio._core._thread_cache (conditional), trio._core._run (conditional), trio.testing._check_streams (top-level), trio.testing._checkpoints (conditional), trio.testing._memory_streams (top-level), trio._highlevel_socket (conditional), trio.testing._raises_group (conditional), _pytest._code.code (top-level), pluggy._hooks (top-level), pluggy._manager (top-level), pluggy._tracing (top-level), pluggy._callers (top-level), _pytest._code.source (top-level), _pytest._io.terminalwriter (top-level), _pytest.compat (top-level), _pytest._py.error (top-level), _pytest._py.path (top-level), _pytest.outcomes (top-level), _pytest.config (top-level), _pytest.config.compat (top-level), _pytest.config.findpaths (top-level), _pytest.pathlib (top-level), _pytest.config.argparsing (top-level), _pytest.hookspec (top-level), _pytest.fixtures (top-level), _pytest.nodes (top-level), _pytest.mark (top-level), _pytest.mark.expression (top-level), _pytest.mark.structures (top-level), _pytest.raises (conditional), _pytest.assertion (top-level), _pytest.assertion.rewrite (top-level), _pytest.assertion.util (top-level), _pytest._io.pprint (top-level), _pytest.python_api (top-level), _pytest.main (top-level), _pytest.reports (top-level), _pytest.runner (top-level), _pytest.terminal (top-level), _pytest.warnings (top-level), _pytest.python (top-level), _pytest.cacheprovider (top-level), _pytest.helpconfig (top-level), trio.testing._sequencer (conditional), trio.testing._trio_test (conditional), trio._file_io (top-level), trio._core._io_windows (conditional), trio._core._generated_io_kqueue (conditional), trio._core._io_kqueue (conditional), trio._core._generated_run (conditional), trio._core._ki (conditional), trio._channel (top-level), trio._dtls (conditional), OpenSSL.SSL (top-level), cryptography.utils (top-level), cryptography.x509.name (top-level), cryptography.x509.base (top-level), cryptography.hazmat.bindings.openssl.binding (top-level), cryptography.x509.extensions (top-level), OpenSSL.crypto (top-level), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), trio._highlevel_open_unix_stream (conditional), trio._highlevel_serve_listeners (top-level), trio._highlevel_ssl_helpers (conditional), trio._path (conditional), trio._signals (conditional), trio._ssl (conditional), trio._timeouts (conditional), parso.python.tree (optional), sqlite3.dbapi2 (top-level), matplotlib (top-level), matplotlib.cbook (top-level), matplotlib._path (top-level), matplotlib.colors (top-level), PIL.Image (top-level), PIL._typing (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.ImagePalette (top-level), PIL.ImageFilter (top-level), PIL.PngImagePlugin (top-level), pyparsing.core (top-level), pyparsing.results (top-level), markupsafe (top-level), cycler (top-level), matplotlib.cm (top-level), matplotlib.markers (top-level), matplotlib._mathtext (conditional), matplotlib.axes._base (top-level), matplotlib.spines (top-level), tornado.gen (top-level), tornado.httputil (top-level), matplotlib.pyplot (conditional), matplotlib.typing (top-level), jupyter_client.jsonutil (top-level), shelve (top-level), pydantic.v1.validators (top-level), pydantic._internal._validate_call (top-level), starlette.exceptions (top-level), anyio._core._eventloop (top-level), anyio.abc._eventloop (top-level), anyio._core._exceptions (top-level), anyio._core._tasks (top-level), anyio.abc._tasks (top-level), anyio._core._testing (top-level), anyio.from_thread (top-level), anyio.abc._sockets (top-level), anyio._core._typedattr (top-level), anyio.abc._streams (top-level), anyio._core._sockets (top-level), anyio.to_thread (top-level), anyio.streams.stapled (top-level), anyio.streams.tls (top-level), anyio.abc._testing (top-level), anyio._core._fileio (top-level), anyio._core._signals (top-level), anyio._core._subprocesses (top-level), anyio._core._tempfile (top-level), starlette.middleware (top-level), uvicorn.config (top-level), click.core (top-level), click.types (top-level), click._compat (top-level), click._winconsole (top-level), click.exceptions (top-level), click.utils (top-level), click.shell_completion (top-level), click.formatting (top-level), click.parser (top-level), click._textwrap (top-level), click.termui (top-level), click._termui_impl (top-level), uvicorn._types (top-level), uvicorn.middleware.wsgi (top-level), uvicorn.server (top-level), uvicorn.protocols.websockets.websockets_impl (top-level), uvicorn.supervisors.basereload (top-level), uvicorn.supervisors.statreload (top-level), pydantic_settings.main (top-level), pydantic_settings.sources.types (top-level), pydantic_settings.sources.utils (top-level), pydantic_settings.sources.providers.aws (top-level), pydantic_settings.sources.providers.env (top-level), pydantic_settings.sources.providers.azure (top-level), pydantic_settings.sources.providers.cli (top-level), pydantic_settings.sources.providers.dotenv (top-level), pydantic_settings.sources.providers.gcp (top-level), google.auth.jwt (optional), cachetools (top-level), urllib3._collections (optional), requests.compat (top-level), socks (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), multidict._abc (top-level), multidict._multidict_py (top-level), multidict (conditional), yarl._query (top-level), yarl._url (top-level), propcache._helpers_py (top-level), yarl._path (top-level), aiohttp.web (top-level), aiohttp.abc (top-level), frozenlist (top-level), aiohttp.client_reqrep (top-level), aiohttp.multipart (top-level), aiohttp.compression_utils (conditional), aiohttp.payload (top-level), aiodns (top-level), pycares (top-level), aiohttp.connector (conditional), aiohttp.web_response (top-level), aiohttp.client_middlewares (top-level), aiohttp.cookiejar (top-level), httpx._models (top-level), pandas._typing (top-level), pytz.lazy (optional), pandas.util._exceptions (conditional), pandas._config.config (conditional), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), torch.types (top-level), torch.autograd (top-level), torch.nn.modules.module (top-level), torch._prims_common (top-level), sympy.core.basic (top-level), sympy.core.containers (top-level), sympy.core.expr (top-level), sympy.core.function (top-level), sympy.logic.boolalg (top-level), sympy.printing.conventions (top-level), sympy.sets.sets (top-level), sympy.matrices.matrixbase (top-level), sympy.combinatorics.permutations (top-level), sympy.ntheory.factor_ (top-level), sympy.tensor.indexed (top-level), sympy.tensor.array.ndim_array (top-level), sympy.tensor.array.arrayop (top-level), sympy.tensor.array.expressions.array_expressions (top-level), sympy.polys.puiseux (conditional), sympy.matrices.sparse (top-level), sympy.strategies.core (top-level), sympy.tensor.array.expressions.from_array_to_indexed (top-level), sympy.tensor.functions (top-level), sympy.vector.coordsysrect (top-level), sympy.solvers.polysys (top-level), sympy.physics.units.util (top-level), scipy._lib._docscrape (top-level), scipy.stats._stats_py (top-level), scipy.spatial.distance (top-level), scipy._lib.doccer (top-level), scipy.integrate._quadrature (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._qmc (top-level), scipy.stats._resampling (top-level), scipy.stats._multicomp (top-level), scipy.stats._sensitivity_analysis (top-level), scipy.ndimage._filters (top-level), scipy.ndimage._ni_support (top-level), scipy.linalg._decomp_cossin (top-level), sympy.plotting.series (top-level), sympy.plotting.backends.matplotlibbackend.matplotlib (top-level), sympy.integrals.manualintegrate (top-level), sympy.printing.tensorflow (top-level), torch.fx.immutable_collections (top-level), torch.utils._pytree (top-level), torch.utils._cxx_pytree (top-level), torch.export (top-level), torch.fx.graph (top-level), torch.fx.node (top-level), torch.distributed.distributed_c10d (top-level), torch._strobelight.cli_function_profiler (top-level), torch.utils._backport_slots (conditional), torch.utils.weak (top-level), torch._inductor.utils (top-level), torch._dynamo.device_interface (top-level), torch.utils._ordered_set (top-level), torch.fx.experimental.symbolic_shapes (top-level), torch._logging.structured (top-level), torch.overrides (top-level), torch.package.package_exporter (top-level), torch._decomp (top-level), torch._dispatch.python (top-level), torch.utils._python_dispatch (top-level), torchgen.code_template (conditional), torchgen.utils (conditional), torchgen.model (conditional), torchgen.local (conditional), torchgen.api.types.signatures (conditional), torchgen.api.dispatcher (conditional), torchgen.api.native (conditional), torchgen.api.translate (conditional), torchgen.api.cpp (conditional), torch.nn.attention (top-level), torch.fx.experimental.proxy_tensor (top-level), torch._library.utils (top-level), torch.utils._sympy.functions (conditional), torch._meta_registrations (top-level), torch._prims (top-level), torch.library (top-level), torch._library.custom_ops (top-level), torch._functorch.utils (top-level), torch._library.triton (top-level), torch._dynamo.symbolic_convert (top-level), pandas.core.frame (top-level), pandas.util._validators (top-level), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.util._decorators (conditional), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), fsspec.dircache (top-level), fsspec.mapping (top-level), pyarrow.vendored.docscrape (top-level), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.masked (conditional), numba.core.typing.context (top-level), numba.core.typing.templates (top-level), numba.core.callconv (top-level), numba.experimental.jitclass.base (top-level), numba.misc.llvm_pass_timings (top-level), numba.core.annotations.type_annotations (top-level), numba.core.types.containers (top-level), numba.typed.typeddict (top-level), numba.typed.typedlist (top-level), numba.core.utils (top-level), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.groupby.groupby (top-level), pandas.core.arrays.numeric (conditional), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.numpy_ (conditional), pandas.io.formats.format (top-level), pandas.core.arrays.timedeltas (conditional), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.indexing (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io._util (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), pandas.core.arrays.string_arrow (conditional), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.internals.blocks (conditional), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.arrow.array (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlalchemy.util.compat (conditional), sqlalchemy.util.typing (top-level), sqlalchemy.sql.coercions (top-level), sqlalchemy.sql.traversals (top-level), sqlalchemy.sql.compiler (top-level), sqlalchemy.sql.dml (top-level), sqlalchemy.sql.sqltypes (top-level), sqlalchemy.engine.row (top-level), sqlalchemy.sql.lambdas (top-level), sqlalchemy.engine.url (top-level), sqlalchemy.orm.query (top-level), pandas.core.arrays.base (conditional), pandas.core.internals.concat (conditional), pandas.core.indexes.base (conditional), pandas.core.dtypes.cast (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), pyarrow.pandas_compat (top-level), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), torch._dynamo.bytecode_transformation (top-level), torch._dynamo.variables.base (top-level), torch._dynamo.current_scope_id (top-level), torch._dynamo.polyfills (top-level), torch._numpy._ndarray (top-level), torch._numpy._funcs_impl (conditional), torch._numpy.linalg (conditional), torch.random (top-level), torch.autograd.profiler (top-level), torch.optim.lr_scheduler (top-level), torch.optim.optimizer (top-level), torch.optim.swa_utils (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch._inductor.scheduler (conditional), google.protobuf.internal.containers (top-level), google.protobuf.internal.well_known_types (top-level), PIL.ImageDraw (top-level), torch._inductor.runtime.runtime_utils (conditional), cryptography.x509.ocsp (top-level), torch.utils._appending_byte_serializer (top-level), torch._inductor.cudagraph_utils (conditional), torch._inductor.freezing_utils (top-level), torch._inductor.output_code (conditional), torch._inductor.ir (top-level), torch._export.serde.union (top-level), torch._export.serde.serialize (top-level), torch.export.exported_program (top-level), torch._functorch.make_functional (top-level), torch.nn.utils.parametrize (top-level), torch.nn.modules.container (top-level), torch.nn.utils.rnn (top-level), torch.nn.utils._named_member_accessor (top-level), torch.nn.utils.convert_parameters (top-level), torch._functorch.aot_autograd (top-level), torch.fx.proxy (top-level), torch.testing._comparison (top-level), torch.testing._creation (top-level), torch.testing._internal.logging_tensor (top-level), torch.nn.parallel.data_parallel (top-level), torch.nn.parallel.parallel_apply (top-level), torch.nn.parallel.replicate (top-level), torch.cuda.nccl (top-level), torch.jit (top-level), torch.jit._monkeytype_config (top-level), torch._export.converter (top-level), torch.ao.quantization.pt2e._numeric_debugger (top-level), torch.ao.quantization.pt2e.graph_utils (top-level), torch.fx.passes.tools_common (top-level), torch._refs (top-level), torch._prims_common.wrappers (top-level), torch._refs.fft (top-level), torch._decomp.decompositions (top-level), torch.ao.quantization.fx.match_utils (top-level), tabulate (top-level), torch._inductor.inductor_prims (conditional), torch.utils._sympy.symbol (top-level), torch._inductor.dtype_propagation (top-level), torch._inductor.dependencies (top-level), torch._inductor.loop_body (conditional), torch._inductor.index_propagation (top-level), torch._inductor.sizevars (top-level), torch._inductor.codegen.simd_kernel_features (conditional), torch._inductor.codegen.simd (conditional), torch._inductor.codegen.triton (top-level), torch._inductor.codegen.cpp_utils (top-level), torch._inductor.codegen.wrapper (conditional), torch._inductor.codegen.cpp (top-level), torch._inductor.cpp_builder (top-level), torch.torch_version (top-level), torch.utils.hipify.hipify_python (top-level), torch._inductor.codegen.memory_planning (conditional), torch._inductor.codegen.cuda.cuda_cpp_scheduling (top-level), torch._inductor.codegen.rocm.rocm_cpp_scheduling (top-level), torch._inductor.codegen.rocm.rocm_template_buffer (top-level), torch._inductor.codegen.cuda_combined_scheduling (conditional), torch._inductor.debug (top-level), torch._dynamo.repro.after_aot (top-level), torch._dynamo.testing (top-level), torch._dynamo.backends.registry (top-level), torch._dynamo.variables.functions (top-level), torch.distributed.fsdp._flat_param (top-level), torch.distributed.fsdp._common_utils (top-level), torch.distributed.algorithms._checkpoint.checkpoint_wrapper (top-level), torch.autograd.graph (top-level), torch.testing._internal.common_utils (top-level), torch.onnx._internal.exporter._dynamic_shapes (conditional), torch.onnx._internal.exporter._ir_passes (conditional), torch.onnx._internal.exporter._onnx_program (conditional), torch.onnx._internal.exporter._core (top-level), torch.export.graph_signature (top-level), torch._functorch._aot_autograd.schemas (top-level), torch._functorch._aot_autograd.subclass_utils (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (conditional), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.diagnostics.infra._infra (conditional), torch.onnx._internal.diagnostics.infra.utils (conditional), torch.onnx._internal.diagnostics.infra.context (conditional), torch.onnx._internal.diagnostics._diagnostic (conditional), itsdangerous.serializer (top-level), itsdangerous.signer (top-level), itsdangerous.timed (top-level), huggingface_hub.keras_mixin (top-level), torch.distributed.tensor._op_schema (top-level), torch.distributed.tensor._ops.utils (top-level), torch.distributed.tensor._api (top-level), torch.distributed.tensor._dispatch (top-level), torch.distributed.tensor._sharding_prop (top-level), torch.distributed.tensor._utils (top-level), torch.distributed.checkpoint._extension (top-level), torch.distributed.checkpoint._fsspec_filesystem (top-level), torch.distributed.checkpoint.filesystem (top-level), torch.distributed._shard._utils (top-level), torch.distributed._shard.sharded_tensor.utils (top-level), torch.distributed.rpc (top-level), torch.distributed.nn.api.remote_module (top-level), torch.distributed._shard.sharded_tensor.api (conditional), torch.distributed.checkpoint.metadata (top-level), torch.distributed._state_dict_utils (top-level), torch.distributed.checkpoint.utils (top-level), torch.distributed.checkpoint._traverse (top-level), torch.distributed.checkpoint.optimizer (top-level), torch.distributed.elastic.utils.store (top-level), torch.distributed.tensor._ops._math_ops (top-level), torch.distributed.tensor._ops._pointwise_ops (top-level), torch.distributed.tensor._ops._tensor_ops (top-level), torch.distributed.tensor._ops._view_ops (top-level), transformers.utils.generic (top-level), transformers.models.albert.configuration_albert (top-level), transformers.cache_utils (top-level), transformers.generation.logits_process (top-level), transformers.models.auto.auto_factory (top-level), transformers.models.auto.configuration_auto (top-level), transformers.utils.backbone_utils (top-level), transformers.image_processing_utils (top-level), transformers.image_transforms (top-level), transformers.image_utils (top-level), transformers.image_processing_utils_fast (top-level), transformers.video_utils (top-level), transformers.tokenization_utils_base (top-level), transformers.modeling_tf_utils (top-level), transformers.trainer_pt_utils (top-level), torch.utils.data.dataloader (top-level), torch.utils.data.datapipes._typing (top-level), torch.utils.data.datapipes.datapipe (top-level), torch.utils.data.datapipes.utils.common (top-level), torch.utils.data.dataset (top-level), torch.utils.data.datapipes.dataframe.structures (top-level), torch.utils.data.datapipes.iter.callable (top-level), torch.utils.data.datapipes.iter.combinatorics (top-level), torch.utils.data.sampler (top-level), torch.utils.data.datapipes.iter.combining (top-level), torch.utils.data.datapipes.iter.filelister (top-level), torch.utils.data.datapipes.iter.fileopener (top-level), torch.utils.data.datapipes.iter.grouping (top-level), torch.utils.data.datapipes.iter.sharding (top-level), torch.utils.data.datapipes.iter.routeddecoder (top-level), torch.utils.data.datapipes.iter.selecting (top-level), torch.utils.data.datapipes.iter.streamreader (top-level), torch.utils.data.datapipes.map.combinatorics (top-level), torch.utils.data.datapipes.map.combining (top-level), torch.utils.data.datapipes.map.grouping (top-level), torch.utils.data.graph (top-level), torch.utils.data.distributed (top-level), transformers.tokenization_utils_fast (top-level), transformers.models.roformer.configuration_roformer (top-level), transformers.onnx.config (top-level), transformers.onnx.convert (top-level), sklearn.utils._param_validation (top-level), sklearn.utils.validation (top-level), sklearn.externals.array_api_extra._delegation (top-level), sklearn.externals.array_api_extra._lib._backends (top-level), sklearn.externals.array_api_extra._lib._funcs (top-level), sklearn.externals.array_api_extra._lib._at (top-level), sklearn.externals.array_api_extra._lib._utils._helpers (top-level), sklearn.externals.array_api_extra._lib._lazy (top-level), sklearn.utils.multiclass (top-level), sklearn.model_selection._classification_threshold (top-level), sklearn.model_selection._split (top-level), sklearn.utils._plotting (top-level), sklearn.model_selection._search (top-level), sklearn.utils._testing (top-level), torch._higher_order_ops.flex_attention (top-level), transformers.trainer (top-level), transformers.data.data_collator (top-level), transformers.models.bert.configuration_bert (top-level), transformers.models.aria.image_processing_aria (top-level), transformers.models.bart.configuration_bart (top-level), transformers.models.beit.configuration_beit (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.big_bird.configuration_big_bird (top-level), transformers.models.bigbird_pegasus.configuration_bigbird_pegasus (top-level), transformers.models.blenderbot.configuration_blenderbot (top-level), transformers.models.blenderbot_small.configuration_blenderbot_small (top-level), transformers.models.bloom.configuration_bloom (top-level), transformers.models.bridgetower.image_processing_bridgetower (top-level), transformers.models.bridgetower.image_processing_bridgetower_fast (top-level), transformers.models.camembert.configuration_camembert (top-level), transformers.models.chinese_clip.configuration_chinese_clip (top-level), transformers.models.clip.configuration_clip (top-level), transformers.models.codegen.configuration_codegen (top-level), transformers.models.conditional_detr.configuration_conditional_detr (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (top-level), transformers.models.convbert.configuration_convbert (top-level), transformers.models.convnext.configuration_convnext (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.cvt.modeling_tf_cvt (top-level), transformers.models.data2vec.configuration_data2vec_text (top-level), transformers.models.data2vec.configuration_data2vec_vision (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.deberta.configuration_deberta (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.configuration_deberta_v2 (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deformable_detr.image_processing_deformable_detr (top-level), transformers.models.deit.configuration_deit (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.deprecated.deta.image_processing_deta (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.configuration_mega (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.detr.configuration_detr (top-level), transformers.models.detr.image_processing_detr (top-level), transformers.models.dinov2.configuration_dinov2 (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.configuration_distilbert (top-level), transformers.models.donut.modeling_donut_swin (top-level), transformers.models.dpt.image_processing_dpt (top-level), transformers.models.dpt.image_processing_dpt_fast (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.configuration_efficientnet (top-level), transformers.models.electra.configuration_electra (top-level), transformers.models.emu3.image_processing_emu3 (top-level), transformers.models.ernie.configuration_ernie (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.esm.openfold_utils.chunk_utils (top-level), transformers.models.esm.openfold_utils.residue_constants (top-level), transformers.models.esm.openfold_utils.rigid_utils (top-level), transformers.models.esm.openfold_utils.protein (top-level), torch.amp.grad_scaler (conditional), transformers.models.flaubert.configuration_flaubert (top-level), transformers.models.flava.image_processing_flava (top-level), transformers.models.flava.image_processing_flava_fast (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.gemma3.modeling_gemma3 (top-level), transformers.models.gpt2.configuration_gpt2 (top-level), transformers.models.gpt_neo.configuration_gpt_neo (top-level), transformers.models.gptj.configuration_gptj (top-level), transformers.models.granite_speech.feature_extraction_granite_speech (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (top-level), transformers.models.groupvit.configuration_groupvit (top-level), transformers.models.groupvit.modeling_groupvit (top-level), transformers.models.groupvit.modeling_tf_groupvit (top-level), transformers.models.ibert.configuration_ibert (top-level), transformers.models.idefics2.image_processing_idefics2 (top-level), transformers.models.idefics3.image_processing_idefics3 (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.configuration_imagegpt (top-level), transformers.models.internvl.modeling_internvl (top-level), transformers.models.janus.image_processing_janus (top-level), transformers.models.layoutlm.configuration_layoutlm (top-level), transformers.models.layoutlmv3.configuration_layoutlmv3 (top-level), transformers.models.layoutlmv3.image_processing_layoutlmv3 (top-level), transformers.models.xlm_roberta.configuration_xlm_roberta (top-level), transformers.models.levit.configuration_levit (top-level), transformers.models.levit.image_processing_levit (top-level), transformers.models.llava_next.image_processing_llava_next (top-level), transformers.models.llava_onevision.image_processing_llava_onevision (top-level), transformers.models.llava_onevision.processing_llava_onevision (top-level), transformers.models.longformer.configuration_longformer (top-level), transformers.models.longt5.configuration_longt5 (top-level), transformers.models.luke.tokenization_luke (top-level), transformers.models.m2m_100.configuration_m2m_100 (top-level), transformers.models.marian.configuration_marian (top-level), html5lib._utils (optional), html5lib._trie._base (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional), transformers.models.mask2former.image_processing_mask2former (top-level), transformers.models.swin.configuration_swin (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.maskformer.image_processing_maskformer (top-level), transformers.models.maskformer.modeling_maskformer_swin (top-level), transformers.models.mbart.configuration_mbart (top-level), transformers.models.mgp_str.modeling_mgp_str (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.mobilebert.configuration_mobilebert (top-level), transformers.models.mobilenet_v1.configuration_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.configuration_mobilenet_v2 (top-level), transformers.models.mobilevit.configuration_mobilevit (top-level), transformers.models.mobilevitv2.configuration_mobilevitv2 (top-level), transformers.models.mt5.configuration_mt5 (top-level), transformers.models.t5.configuration_t5 (top-level), networkx.utils.misc (top-level), networkx.utils.backends (delayed), networkx.convert (top-level), networkx.classes.coreviews (top-level), networkx.classes.reportviews (top-level), networkx.algorithms.lowest_common_ancestors (top-level), networkx.algorithms.approximation.kcomponents (top-level), networkx.generators.interval_graph (top-level), networkx.drawing.nx_pylab (delayed), sklearn.feature_extraction.text (top-level), sklearn.feature_extraction._dict_vectorizer (top-level), sklearn.datasets._arff_parser (top-level), sklearn.datasets._samples_generator (top-level), nltk.lm.counter (top-level), nltk.lm.vocabulary (top-level), transformers.models.oneformer.image_processing_oneformer (top-level), transformers.models.owlvit.configuration_owlvit (top-level), transformers.models.perceiver.configuration_perceiver (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.pixtral.modeling_pixtral (top-level), transformers.models.plbart.configuration_plbart (top-level), transformers.models.poolformer.configuration_poolformer (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.prompt_depth_anything.image_processing_prompt_depth_anything (top-level), transformers.models.prophetnet.tokenization_prophetnet (top-level), transformers.models.pvt.configuration_pvt (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.rag.retrieval_rag (top-level), transformers.models.rembert.configuration_rembert (top-level), transformers.models.resnet.configuration_resnet (top-level), transformers.models.roberta.configuration_roberta (top-level), transformers.models.roberta_prelayernorm.configuration_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (top-level), transformers.models.segformer.configuration_segformer (top-level), transformers.models.seggpt.modeling_seggpt (top-level), transformers.models.sew_d.modeling_sew_d (top-level), torch.onnx._internal.jit_utils (top-level), torch.onnx._internal.registration (top-level), torch.onnx._internal.onnx_proto_utils (conditional), torch.onnx.utils (conditional), torch.onnx.symbolic_helper (conditional), torch.onnx.symbolic_opset9 (conditional), torch.onnx.symbolic_opset10 (conditional), torch.onnx.symbolic_opset11 (conditional), transformers.models.shieldgemma2.processing_shieldgemma2 (top-level), transformers.models.smolvlm.image_processing_smolvlm (top-level), transformers.models.squeezebert.configuration_squeezebert (top-level), transformers.models.swiftformer.configuration_swiftformer (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swiftformer.modeling_tf_swiftformer (top-level), transformers.models.swin2sr.modeling_swin2sr (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.table_transformer.configuration_table_transformer (top-level), transformers.models.tapas.tokenization_tapas (top-level), transformers.models.timesfm.modeling_timesfm (top-level), transformers.models.tvp.image_processing_tvp (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.configuration_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.image_processing_vilt (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.vision_encoder_decoder.configuration_vision_encoder_decoder (top-level), transformers.models.vit.configuration_vit (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.vit_mae.modeling_vit_mae (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vitdet.modeling_vitdet (top-level), transformers.models.vitpose_backbone.modeling_vitpose_backbone (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), transformers.models.whisper.configuration_whisper (top-level), transformers.models.whisper.english_normalizer (top-level), transformers.models.whisper.generation_whisper (top-level), transformers.models.xlm.configuration_xlm (top-level), transformers.models.xlm_roberta_xl.configuration_xlm_roberta_xl (top-level), transformers.models.xmod.configuration_xmod (top-level), transformers.models.yolos.configuration_yolos (top-level), transformers.models.yolos.image_processing_yolos (top-level), transformers.models.yolos.modeling_yolos (top-level), transformers.models.zoedepth.image_processing_zoedepth (top-level), transformers.hf_argparser (top-level), transformers.pipelines.question_answering (top-level), transformers.pipelines.image_text_to_text (top-level), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.passes.decomp (conditional), torch._functorch.functional_call (top-level), torch.onnx._internal.fx.passes.modularization (conditional), torch.onnx._internal.fx.passes.readability (conditional), torch.onnx._internal.fx.passes.type_promotion (conditional), torch.onnx._internal.onnxruntime (top-level), torch._prims.context (top-level), torch.onnx._internal.io_adapter (conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx._internal.fx.dynamo_graph_extractor (conditional), torch.onnx._internal.fx.fx_symbolic_graph_extractor (conditional), torch.onnx._internal.fx.decomposition_skip (conditional), torch.onnx._internal.fx.fx_onnx_interpreter (conditional), torch.fx.passes.infra.partitioner (top-level), torch.onnx.symbolic_opset17 (top-level), torch.onnx.symbolic_opset18 (top-level), torch.onnx (conditional), torch.onnx._internal.exporter._compat (top-level), torch.testing._internal.common_device_type (top-level), torch.distributed.utils (top-level), torch.nn.parallel.scatter_gather (top-level), torch.distributed.fsdp.wrap (top-level), torch.distributed.fsdp.api (top-level), torch.distributed._composable.checkpoint_activation (top-level), torch.distributed._composable.replicate (top-level), torch.distributed.fsdp._fully_shard._fsdp_state (top-level), torch.distributed.fsdp._fully_shard._fsdp_param (top-level), torch.distributed.fsdp._fully_shard._fully_shard (conditional), torch.distributed.fsdp.fully_sharded_data_parallel (top-level), torch.distributed.fsdp._init_utils (top-level), torch.distributed.fsdp._optim_utils (top-level), torch.distributed.fsdp._debug_utils (top-level), torch.distributed.fsdp._state_dict_utils (top-level), torch.distributed.fsdp._unshard_param_utils (top-level), torch._higher_order_ops.triton_kernel_wrap (top-level), torch._dynamo.side_effects (top-level), torch._dynamo.create_parameter_op (top-level), _pytest.recwarn (top-level), torch._dynamo.variables.builder (top-level), torch._dynamo.mutation_guard (top-level), torch._dynamo.variables.builtin (top-level), torch._dynamo.variables.torch (top-level), torch._functorch._aot_autograd.subclass_parametrization (top-level), torch._inductor.pattern_matcher (top-level), torch.utils.flop_counter (top-level), torch._inductor.lowering (top-level), torch._inductor.subgraph_lowering (top-level), torch._higher_order_ops.auto_functionalize (top-level), torch.distributed._symmetric_memory (top-level), torch._inductor.autotune_process (top-level), torch._inductor.codegen.cpp_wrapper_cpu (top-level), torch._inductor.kernel.mm_common (top-level), torch._inductor.codegen.cpp_template_kernel (top-level), torch._inductor.codegen.cpp_template (top-level), torch._inductor.codegen.rocm.rocm_template (top-level), torch._inductor.codegen.rocm.rocm_benchmark_request (conditional), torch._inductor.codegen.rocm.rocm_kernel (top-level), torch._inductor.mkldnn_ir (top-level), torch._inductor.fx_passes.ddp_fusion (top-level), torch._inductor.fx_passes.group_batch_fusion (top-level), torch._inductor.fx_passes.pre_grad (top-level), torch.fx.experimental.optimization (top-level), torch._inductor.fx_passes.split_cat (top-level), torch._inductor.fx_passes.pad_mm (top-level), torch._inductor.compile_fx (conditional), torch._export.utils (top-level), torch._export.verifier (top-level), torch.export._unlift (top-level), torch._inductor.compile_fx_ext (conditional), torch._inductor.compile_fx_subproc (conditional), torch._inductor.cudagraph_trees (conditional), torch._dynamo.graph_deduplication (top-level), torch._inductor.codegen.common (conditional), torch._inductor.codegen.halide (conditional), torch.ao.nn.quantized.modules.linear (top-level), torch.distributed.optim.apply_optimizer_in_backward (top-level), torch.distributed.optim.named_optimizer (top-level), torch.distributed.algorithms.model_averaging.averagers (top-level), torch.distributed.algorithms.model_averaging.utils (top-level), torch._functorch._aot_autograd.runtime_wrappers (conditional), torch._functorch._aot_autograd.jit_compile_runtime_wrappers (conditional), torch._export.pass_infra.proxy_value (top-level), torch._inductor.graph (conditional), torch._inductor.runtime.triton_heuristics (conditional), torch._inductor.codecache (conditional), torch.cuda.random (top-level), torch.utils._cpp_embed_headers (top-level), torch.xpu.random (top-level), torch._dynamo.polyfills.builtins (conditional), torch._dynamo.polyfills.functools (top-level), torch._dynamo.polyfills.itertools (conditional), torch._dynamo.polyfills.pytree (conditional), torch.testing._internal.optests.generate_tests (top-level), torch._subclasses.fake_tensor (conditional), torch.package.glob_group (top-level), torch.package.analyze.trace_dependencies (top-level), torch.package.package_importer (top-level), torch._dynamo.metrics_context (top-level), torch._dynamo.utils (conditional), torch._dynamo.callback (top-level), torch.distributed.rendezvous (top-level), torch.fx.passes.splitter_base (top-level), torch.fx.interpreter (conditional), torch.nn.modules.adaptive (top-level), torch.nn.modules.padding (top-level), torch.autograd.gradcheck (top-level), torch.functional (top-level), torch.signal.windows.windows (top-level), torch.cuda._sanitizer (top-level), pandas.core.arrays.arrow.accessors (conditional), pandas.core.dtypes.dtypes (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), lxml.html (top-level), lxml.html._setmixin (optional), cssselect.parser (conditional), cssselect.xpath (top-level), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), seaborn._core.typing (top-level), seaborn._base (top-level), seaborn._core.data (top-level), seaborn.external.docscrape (top-level), seaborn._core.scales (top-level), seaborn._core.plot (top-level), seaborn._marks.base (top-level), seaborn._stats.base (top-level), seaborn._core.subplots (top-level), patsy.constraint (optional), statsmodels.tools.validation.validation (top-level), statsmodels.tools.typing (top-level), statsmodels.base.optimizer (top-level), statsmodels.regression.linear_model (top-level), scipy.signal._short_time_fft (top-level), statsmodels.stats.diagnostic (top-level), statsmodels.tools.docstring (top-level), tweepy.mixins (top-level), blinker.base (top-level), blinker._utilities (top-level), ccxt.static_dependencies.ethereum.utils.types (top-level), ccxt.static_dependencies.toolz.itertoolz (top-level), ccxt.static_dependencies.toolz.dicttoolz (top-level), ccxt.static_dependencies.ethereum.account.messages (top-level), ccxt.static_dependencies.marshmallow.schema (top-level), ccxt.static_dependencies.marshmallow.fields (top-level), ccxt.static_dependencies.marshmallow.utils (top-level), ccxt.static_dependencies.marshmallow.orderedset (top-level), praw.models.reddit.collections (conditional), scrapy.http.headers (top-level), w3lib.http (top-level), scrapy.utils.datatypes (top-level), scrapy.utils.python (top-level), scrapy.utils.asyncgen (top-level), w3lib.url (top-level), scrapy.utils.curl (conditional), scrapy.utils.trackref (conditional), scrapy.http.request (conditional), setuptools.command.build_py (top-level), twisted.python.compat (top-level), scrapy.http.request.form (top-level), w3lib.html (top-level), scrapy.http.response (conditional), hyperlink._url (optional), twisted.web.error (top-level), scrapy.utils.response (conditional), scrapy.http.response.text (conditional), scrapy.item (top-level), scrapy.utils.url (conditional), scrapy.spiders (conditional), scrapy.settings (top-level), scrapy.utils.misc (top-level), scrapy.utils.conf (conditional), scrapy.core.scraper (top-level), scrapy.core.spidermw (top-level), scrapy.utils.defer (top-level), scrapy.utils.reactor (conditional), scrapy.middleware (conditional), scrapy.utils.log (top-level), scrapy.utils.request (conditional), scrapy.utils.spider (conditional), scrapy.utils.signal (top-level), scrapy.core.engine (conditional), scrapy.core.downloader.handlers (conditional), scrapy.core.downloader.middleware (top-level), scrapy.resolver (conditional), queuelib.pqueue (conditional), queuelib.rrqueue (conditional), scrapy.pqueues (conditional), scrapy.utils.ossignal (top-level), scrapy.crawler (conditional), scrapy.spiders.crawl (top-level), scrapy.linkextractors (conditional), scrapy.linkextractors.lxmlhtml (top-level), scrapy.utils.iterators (conditional), scrapy.spiders.feed (conditional), scrapy.spiders.sitemap (top-level), scrapy.utils.sitemap (conditional), sklearn.tree._export (top-level), shap.plots._force (top-level), pennylane.numpy.wrapper (top-level), pennylane.capture.switches (top-level), termcolor.termcolor (conditional), pennylane.math.multi_dispatch (top-level), pennylane.operation (top-level), pennylane.typing (top-level), pennylane.wires (top-level), pennylane.pytrees.pytrees (top-level), rustworkx.rustworkx (top-level), pennylane.ops.meta (top-level), pennylane.ops.op_math.composite (top-level), pennylane.tape.tape (top-level), pennylane.measurements.classical_shadow (top-level), pennylane.measurements.measurements (top-level), pennylane.measurements.capture_measurements (top-level), pennylane.measurements.counts (top-level), pennylane.measurements.mid_measure (top-level), pennylane.measurements.expval (top-level), pennylane.measurements.sample (top-level), pennylane.measurements.mutual_info (top-level), pennylane.measurements.probs (top-level), pennylane.measurements.purity (top-level), pennylane.measurements.shots (top-level), pennylane.measurements.state (top-level), pennylane.measurements.var (top-level), pennylane.measurements.vn_entropy (top-level), pennylane.tape.qscript (top-level), pennylane.control_flow.while_loop (top-level), pennylane.ops.op_math.controlled (top-level), pennylane.ops.qubit.observables (top-level), tomlkit.api (top-level), tomlkit._utils (top-level), tomlkit._types (conditional), pennylane.concurrency.executors.external.dask (top-level), pennylane.concurrency.executors.base (top-level), pennylane.concurrency.executors.external.mpi (top-level), pennylane.concurrency.executors.native.api (top-level), pennylane.concurrency.executors.native.multiproc (top-level), pennylane.concurrency.executors.native.serial (top-level), pennylane.transforms.core.transform_dispatcher (top-level), pennylane.transforms.core.transform_program (top-level), pennylane.transforms.batch_input (top-level), pennylane.transforms.compile (top-level), pennylane.transforms.dynamic_one_shot (top-level), pennylane.transforms.insert_ops (top-level), pennylane.transforms.mitigate (top-level), pennylane.transforms.decompose (top-level), pennylane.devices.device_api (top-level), pennylane.devices.preprocess (top-level), pennylane.pulse.convenience_functions (top-level), pennylane.pulse.hardware_hamiltonian (top-level), pennylane.pulse.parametrized_evolution (top-level), pennylane.ops.functions.bind_new_parameters (top-level), pennylane.ops.functions.dot (top-level), pennylane.ops.functions.equal (top-level), pennylane.ops.functions.map_wires (top-level), pennylane.workflow.construct_batch (top-level), pennylane.workflow._cache_transform (top-level), pennylane.workflow.qnode (top-level), pennylane.workflow.resolution (top-level), pennylane.workflow.jacobian_products (top-level), pennylane.workflow.interfaces.autograd (top-level), pennylane.workflow.interfaces.jax (top-level), pennylane.qnn.torch (top-level), pennylane.ops.functions.simplify (top-level), pennylane.pulse.parametrized_hamiltonian_pytree (top-level), pennylane.pulse.transmon (top-level), pennylane.devices.qubit.initialize_state (top-level), pennylane.devices.qubit.measure (top-level), pennylane.devices._legacy_device (top-level), pennylane.devices.default_mixed (top-level), pennylane.devices.qubit_mixed.initialize_state (top-level), pennylane.devices.qubit_mixed.measure (top-level), pennylane.devices.default_clifford (top-level), pennylane.devices.default_qutrit_mixed (top-level), pennylane.devices.qutrit_mixed.initialize_state (top-level), pennylane.devices.qutrit_mixed.measure (top-level), pennylane.templates.tensornetworks.mera (top-level), pennylane.ops.op_math.sum (top-level), pennylane.qaoa.mixers (top-level), pennylane.qaoa.cycle (top-level), pennylane.qaoa.cost (top-level), pennylane.io.io (top-level), cirq.transformers.randomized_measurements (top-level), cirq.qis.entropy (top-level), cirq.protocols.equal_up_to_global_phase_protocol (top-level), qiskit.circuit.quantumcircuit (top-level), qiskit.utils.deprecation (top-level), qiskit.quantum_info.analysis.z2_symmetries (top-level), qiskit.circuit.library.generalized_gates.diagonal (top-level), qiskit.dagcircuit.collect_blocks (top-level), qiskit.circuit.quantumcircuitdata (top-level), qiskit.circuit.instructionset (top-level), qiskit.dagcircuit.dagdependency (top-level), qiskit.circuit.parametertable (top-level), qiskit.passmanager.passmanager (top-level), qiskit.passmanager.base_tasks (top-level), qiskit.passmanager.flow_controllers (top-level), qiskit.transpiler.passmanager (top-level), qiskit.transpiler.basepasses (top-level), rustworkx.visualization.matplotlib (top-level), qiskit.providers.options (top-level), qiskit.transpiler.target (top-level), qiskit.transpiler.passes.routing.algorithms.token_swapper (top-level), qiskit.transpiler.passes.routing.algorithms.util (top-level), qiskit.transpiler.passes.routing.commuting_2q_gate_routing.commuting_2q_block (top-level), qiskit.transpiler.passes.basis.decompose (top-level), qiskit.transpiler.passes.synthesis.high_level_synthesis (top-level), qiskit.synthesis.unitary.aqc.aqc (top-level), qiskit.transpiler.passes.scheduling.padding.base_padding (top-level), qiskit.transpiler.passes.scheduling.alignments.reschedule (top-level), qiskit.synthesis.clifford.clifford_decompose_layers (top-level), qiskit.quantum_info.operators.symplectic.sparse_pauli_op (top-level), qiskit.circuit.library.data_preparation.pauli_feature_map (top-level), qiskit.circuit.library.n_local.n_local (top-level), qiskit.circuit.library.n_local.two_local (top-level), qiskit.circuit.library.n_local.real_amplitudes (top-level), qiskit.circuit.library.n_local.efficient_su2 (top-level), qiskit.circuit.library.n_local.evolved_operator_ansatz (top-level), qiskit.synthesis.evolution.product_formula (top-level), qiskit.circuit.library.n_local.excitation_preserving (top-level), qiskit.circuit.library.data_preparation.initializer (top-level), qiskit.quantum_info.states.stabilizerstate (top-level), qiskit.synthesis.stabilizer.stabilizer_decompose (top-level), qiskit.synthesis.stabilizer.stabilizer_circuit (top-level), qiskit.visualization.timeline.core (top-level), qiskit.synthesis.evolution.lie_trotter (top-level), qiskit.synthesis.evolution.suzuki_trotter (top-level), qiskit.synthesis.evolution.qdrift (top-level), qiskit.circuit.library.arithmetic.piecewise_linear_pauli_rotations (top-level), qiskit.circuit.library.arithmetic.quadratic_form (top-level), qiskit.circuit.library.generalized_gates.mcmt (top-level), qiskit.circuit.library.generalized_gates.gms (top-level), qiskit.circuit.library.fourier_checking (top-level), qiskit.circuit.library.iqp (top-level), qiskit.qasm2.export (top-level), pennylane.optimize.qng (top-level), pennylane.shadows.classical_shadow (top-level), pennylane.qcut.cutcircuit (top-level), pennylane.qcut.cutstrategy (top-level), pennylane.qcut.kahypar (top-level), pennylane.qcut.processing (top-level), pennylane.qcut.utils (top-level), pennylane.qcut.tapes (top-level), pennylane.qcut.cutcircuit_mc (top-level), pennylane.drawer.mpldrawer (top-level), pennylane.data.base.attribute (top-level), pennylane.data.base.hdf5 (top-level), pennylane.data.base._lazy_modules (top-level), pennylane.data.base.dataset (top-level), pennylane.data.base.mapper (top-level), pennylane.data.attributes.dictionary (top-level), pennylane.data.attributes.list (top-level), pennylane.data.attributes.operator.operator (top-level), pennylane.data.attributes.serialization (top-level), pennylane.data.data_manager.foldermap (top-level), pennylane.data.data_manager.params (top-level), dimod.constrained.constrained (top-level), dimod.binary.binary_quadratic_model (top-level), dimod.binary.pybqm (top-level), dimod.sampleset (top-level), dimod.serialization.format (top-level), dimod.serialization.utils (top-level), dimod.typing (top-level), dimod.vartypes (top-level), dimod.variables (top-level), dimod.cyvariables (top-level), dimod.views.samples (top-level), dimod.utilities (top-level), dimod.binary.vartypeview (top-level), dimod.decorators (top-level), dimod.quadratic.quadratic_model (top-level), dimod.serialization.fileview (top-level), dimod.views.quadratic (top-level), dimod.lp (top-level), dimod.generators.bpsp (top-level), dimod.generators.random (top-level), dimod.generators.satisfiability (top-level), dimod.core.sampler (top-level), dimod.core.polysampler (top-level), dimod.higherorder.polynomial (top-level), dimod.discrete.discrete_quadratic_model (top-level), dimod.reference.samplers.null_sampler (top-level), dimod.testing.asserts (top-level), dimod.testing.sampler (top-level), curl_cffi.requests.cookies (top-level), curl_cffi.requests.headers (top-level), curl_cffi.requests.models (top-level), frozendict.monkeypatch (delayed), frozendict.cool (top-level), frozendict (top-level), peewee (conditional, optional), tldextract.tldextract (top-level), tldextract.cache (top-level), tldextract.suffix_list (top-level), aiosqlite.core (top-level), aiosqlite.context (top-level), aiosqlite.cursor (top-level), anyio._backends._asyncio (top-level), anyio._core._asyncio_selector_thread (top-level), anyio._backends._trio (top-level), tifffile.tifffile (top-level), tifffile.zarr (conditional), pydantic.deprecated.decorator (top-level), pydantic.experimental.arguments_schema (top-level), pydantic.experimental.pipeline (top-level), pydantic.mypy (top-level), sklearn.externals.array_api_compat.common._fft (conditional), torch._export.tools (top-level), torch._inductor.fuzzer (top-level), torch.ao.quantization.quantizer.x86_inductor_quantizer (top-level), torch.distributed._shard.sharded_optim (top-level), torch.distributed._shard.sharded_optim.api (top-level), torch.distributed._tools.memory_tracker (top-level), torch.distributed.algorithms.model_averaging.hierarchical_model_averager (top-level), torch.distributed.checkpoint.state_dict (top-level), torch.distributed.elastic.control_plane (top-level), torch.distributed.elastic.utils.data.cycling_iterator (top-level), torch.distributed.fsdp.sharded_grad_scaler (top-level), torch.distributed.pipelining._backward (top-level), torch.distributed.tensor.debug._visualize_sharding (top-level), torch.distributed.tensor.experimental (top-level), torch.distributed.tensor.experimental._attention (top-level), torch.distributed.tensor.experimental._func_map (top-level), torch.distributed.tensor.experimental._register_sharding (top-level), torch.distributed.tensor.experimental._tp_transform (top-level), torch.fx.experimental.debug (top-level), torch.fx.experimental.unification.core (top-level), torch.fx.experimental.unification.unification_tools (top-level), torch.fx.experimental.migrate_gradual_types.constraint_generator (top-level), torch.nn.utils.prune (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level), torch.utils.benchmark.utils.common (top-level), torch.utils.benchmark.utils.valgrind_wrapper.timer_interface (top-level), torch.testing._internal.opinfo.core (top-level), torch.testing._internal.opinfo.utils (top-level), torch.testing._internal.opinfo.definitions._masked (top-level), torch.testing._internal.common_methods_invocations (top-level), torch.testing._internal.opinfo.definitions.linalg (top-level), torch.testing._internal.common_nn (top-level), torch.testing._internal.distributed._tensor.common_dtensor (top-level), torch.testing._internal.hypothesis_utils (top-level), torch.utils._strobelight.cli_function_profiler (top-level), torch.utils.benchmark.examples.spectral_ops_fuzz_test (top-level), torch.utils.bundled_inputs (top-level), gevent.contextvars (optional), gevent.tests.test__local (optional), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), scipy._lib.array_api_compat.common._fft (conditional), scipy.constants._codata (top-level), scrapy.commands (conditional), scrapy.cmdline (conditional), scrapy.commands.bench (conditional), scrapy.utils.test (conditional), scrapy.contracts (top-level), itemadapter.adapter (top-level), scrapy.commands.parse (conditional), scrapy.utils.console (top-level), scrapy.shell (conditional), scrapy.responsetypes (conditional), scrapy.utils.decorators (conditional), scrapy.http.cookies (conditional), scrapy.downloadermiddlewares.cookies (conditional), scrapy.downloadermiddlewares.defaultheaders (conditional), protego._protego (conditional), scrapy.exporters (top-level), scrapy.extensions.feedexport (top-level), scrapy.extensions.httpcache (conditional), scrapy.mail (conditional), scrapy.pipelines.media (conditional), scrapy.pipelines.files (conditional), scrapy.pipelines.images (conditional), scrapy.spidermiddlewares.base (conditional), scrapy.spidermiddlewares.depth (conditional), scrapy.spidermiddlewares.httperror (conditional), scrapy.spiders.init (top-level), scrapy.squeues (conditional), scrapy.utils.testproc (conditional), dbm.dumb (top-level), sqlalchemy.ext.baked (top-level), sqlalchemy.dialects.postgresql.psycopg2 (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), netrc (delayed, conditional), getpass (delayed, optional), setuptools._vendor.backports.tarfile (optional), http.server (delayed, optional), psutil (optional), _pytest._py.path (delayed), twisted.python.util (optional), gevent.subprocess (optional), twisted.protocols.ftp (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), _pytest._py.path (delayed), twisted.python.util (optional), gevent.subprocess (optional), twisted.protocols.ftp (optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level), IPython.utils.timing (optional), fsspec.asyn (conditional, optional), torch._inductor.codecache (delayed, conditional), twisted.internet.process (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), _pyrepl.pager (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level), IPython.core.page (delayed, optional), prompt_toolkit.input.vt100 (top-level), click._termui_impl (conditional), tqdm.utils (delayed, optional), twisted.internet.process (optional)
missing module named 'yaml._yaml' - imported by yaml.cyaml (top-level)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional), torch._jit_internal (optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), rlcompleter (optional), pdb (delayed, optional), site (delayed, optional), pstats (conditional, optional), dill.source (delayed, conditional, optional), sympy.interactive.session (delayed, optional), websockets.__main__ (delayed, optional), scrapy.utils.console (delayed, optional), sqlite3.__main__ (delayed, conditional, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named fcntl - imported by subprocess (optional), _pyrepl.unix_console (top-level), xmlrpc.server (optional), tqdm.utils (delayed, optional), filelock._unix (conditional, optional), tweepy.cache (optional), twisted.python.compat (delayed, optional), twisted.internet.fdesc (optional), pty (delayed, optional), twisted.internet.process (optional), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional), eventlet.greenio.base (delayed, optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed), joblib.externals.loky.backend.fork_exec (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), watchfiles.run (top-level), joblib.externals.loky.backend.context (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), pennylane.concurrency.executors.native.multiproc (top-level), pennylane.concurrency.executors.native.conc_futures (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional), joblib.parallel (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named numpy.typing._UIntLike_co - imported by numpy.typing (conditional), statsmodels.tools.typing (conditional)
missing module named numpy.typing._FloatLike_co - imported by numpy.typing (conditional), statsmodels.tools.typing (conditional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional), sqlalchemy.util.langhelpers (conditional)
missing module named _typeshed - imported by pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), numpy.random.bit_generator (top-level), trio._file_io (conditional), trio._path (conditional), prompt_toolkit.eventloop.inputhook (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), torch.utils._backport_slots (conditional), git.objects.fun (conditional), scrapy.settings (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), scrapy.extensions.feedexport (conditional)
missing module named numpy.random.RandomState - imported by numpy.random (top-level), numpy.random._generator (top-level), networkx.utils.backends (delayed)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), astunparse (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named six.moves.cStringIO - imported by six.moves (top-level), astunparse (top-level)
missing module named StringIO - imported by six (conditional), urllib3.packages.six (conditional), simplejson.compat (conditional, optional), nltk.corpus.reader.timit (delayed, optional), requests_file (optional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named gi - imported by matplotlib.cbook (delayed, conditional), ipykernel.gui.gtk3embed (top-level)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (optional), matplotlib.cbook (optional), sklearn.utils.fixes (conditional), scipy._lib._util (conditional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional, optional), pyarrow (optional), tqdm.version (optional), autoray (optional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named nltk.corpus.CorpusReader - imported by nltk.corpus (delayed, conditional), nltk.corpus.reader.wordnet (delayed, conditional)
missing module named nltk.corpus.reader.CorpusReader - imported by nltk.corpus.reader (top-level), nltk.corpus.reader.wordnet (top-level), nltk.corpus.reader.lin (top-level), nltk.corpus.reader.sentiwordnet (top-level), nltk.corpus.reader.crubadan (top-level), nltk.corpus.reader.bcp47 (top-level)
missing module named sqlite3.Warning - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.sqlite_version - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.Row - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.register_converter - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.register_adapter - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.ProgrammingError - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.NotSupportedError - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.IntegrityError - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.Error - imported by sqlite3 (top-level), aiosqlite (top-level)
missing module named sqlite3.OperationalError - imported by sqlite3 (optional), IPython.core.history (optional), aiosqlite (top-level)
missing module named sqlite3.DatabaseError - imported by sqlite3 (optional), IPython.core.history (optional), aiosqlite (top-level)
missing module named pygame - imported by nltk.corpus.reader.timit (delayed, optional)
missing module named ossaudiodev - imported by nltk.corpus.reader.timit (delayed, optional)
missing module named svgling - imported by nltk.tree.tree (delayed)
missing module named cgi - imported by nltk.tree.prettyprinter (optional), lxml.doctestcompare (optional), lxml.html.diff (optional)
excluded module named pytest - imported by executing._pytest_utils (delayed, optional), _pytest.timing (conditional), _pytest.warnings (top-level), _pytest.mark (delayed), _pytest.helpconfig (top-level), _pytest.config (delayed, conditional, optional), trio.testing._raises_group (conditional, optional), scipy._lib._testutils (delayed), sympy.testing.runtests_pytest (optional), sklearn.utils._testing (optional), networkx.utils.backends (delayed, conditional, optional), torch.testing._internal.common_utils (delayed, conditional, optional), torch.testing._internal.optests.generate_tests (delayed, conditional), pandas._testing._io (delayed), pandas._testing (delayed), patsy.util (delayed), patsy.infix_parser (delayed), patsy.tokens (delayed), patsy.constraint (delayed), patsy.contrasts (delayed), patsy.eval (delayed), patsy.missing (delayed), patsy.categorical (delayed), patsy.splines (delayed), patsy.mgcv_cubic_splines (delayed), patsy.builtins (delayed), patsy.design_info (delayed), patsy.build (delayed), patsy.user_util (delayed), statsmodels.tools._test_runner (delayed), skimage._shared.tester (delayed), fsspec.conftest (top-level), pyarrow.conftest (top-level), pyarrow.tests.util (top-level), torch._numpy.testing.utils (delayed)
missing module named 'cupy_backends.cuda' - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by sklearn.externals.array_api_compat.cupy._typing (top-level), sklearn.externals.array_api_compat.common._helpers (delayed), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by sklearn.externals.array_api_compat.common._helpers (delayed, conditional), transformers.generation.flax_logits_process (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), pennylane.devices.qubit.apply_operation (delayed, optional), pennylane.pulse.parametrized_evolution (optional), pennylane.pulse.parametrized_hamiltonian_pytree (top-level)
missing module named 'jax.numpy' - imported by transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), scipy._lib.array_api_compat.common._helpers (delayed, conditional), pennylane.capture.autograph.ag_primitives (optional), pennylane.math.single_dispatch (delayed), pennylane.math.multi_dispatch (delayed, conditional), pennylane.math.decomposition (optional), pennylane.typing (delayed, conditional), pennylane.control_flow._loop_abstract_axes (delayed), pennylane.pulse.convenience_functions (optional), pennylane.workflow.interfaces.jax_jit (top-level), pennylane.workflow.interfaces.jax (top-level), pennylane.pulse.parametrized_evolution (optional), pennylane.pulse.parametrized_hamiltonian_pytree (top-level), pennylane.devices.qubit.sampling (delayed), pennylane.devices.qutrit_mixed.sampling (delayed), pennylane.gradients.pulse_gradient (optional)
missing module named sparse - imported by scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.dask.array (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), sklearn.externals.array_api_compat.dask.array.fft (top-level), sklearn.externals.array_api_compat.dask.array.linalg (top-level)
missing module named ndonnx - imported by sklearn.externals.array_api_compat.common._helpers (delayed), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), opt_einsum.backends.cupy (delayed), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.cupy (top-level), sklearn.externals.array_api_compat.cupy._aliases (top-level), sklearn.externals.array_api_compat.cupy._info (top-level), sklearn.externals.array_api_compat.cupy._typing (top-level), sklearn.utils._testing (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional), sklearn.externals.array_api_compat.cupy.fft (top-level), sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), opt_einsum.backends.jax (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), sklearn.externals.array_api_compat.common._helpers (delayed), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), scipy._lib._array_api (delayed, conditional), pennylane.capture.switches (optional), pennylane.capture.flatfn (optional), pennylane.capture.autograph.ag_primitives (optional), autoray.compiler (delayed, conditional), pennylane.math.is_independent (delayed), pennylane.math.utils (delayed, conditional), pennylane.math.single_dispatch (delayed), pennylane.math.multi_dispatch (delayed, conditional), pennylane.math.fidelity (delayed), pennylane._grad (optional), pennylane.math.grad (delayed, conditional), pennylane.math.decomposition (optional), pennylane.typing (delayed, conditional), pennylane.operation (optional), pennylane.measurements.capture_measurements (optional), pennylane.measurements.mid_measure (delayed), pennylane.capture.base_interpreter (top-level), pennylane.capture.dynamic_shapes (optional), pennylane.control_flow._loop_abstract_axes (delayed), pennylane.control_flow.for_loop (delayed), pennylane.control_flow.while_loop (delayed), pennylane.ops.op_math.condition (delayed), pennylane.ops.qubit.special_unitary (delayed, conditional), pennylane.transforms.core.transform_dispatcher (delayed, optional), pennylane.transforms.core.cotransform_cache (delayed), pennylane.transforms.core.transform_program (delayed), pennylane.transforms.optimization.cancel_inverses (delayed, optional), pennylane.transforms.optimization.commute_controlled (delayed, optional), pennylane.transforms.optimization.merge_rotations (delayed, optional), pennylane.transforms.optimization.merge_amplitude_embedding (delayed, optional), pennylane.transforms.optimization.single_qubit_fusion (delayed, optional), pennylane.transforms.defer_measurements (delayed, optional), pennylane.transforms.unitary_to_rot (delayed, optional), pennylane.transforms.decompose (delayed, optional), pennylane.devices.qubit.apply_operation (delayed, optional), pennylane.ops.functions.assert_valid (delayed, optional), pennylane.workflow.resolution (delayed, conditional, optional), pennylane.workflow.interfaces.jax_jit (top-level), pennylane.workflow.interfaces.jax (top-level), pennylane.workflow._capture_qnode (top-level), pennylane.ops.functions.map_wires (delayed, optional), pennylane.pulse.parametrized_evolution (optional), pennylane.pulse.parametrized_hamiltonian_pytree (top-level), pennylane.devices.qubit.sampling (delayed), pennylane.devices.qubit.simulate (delayed, conditional), pennylane.devices.qubit.dq_interpreter (top-level), pennylane.devices.default_qubit (delayed, conditional), pennylane.devices.qubit.jaxpr_adjoint (top-level), pennylane.qchem.factorization (optional), pennylane.devices._qubit_device (delayed, conditional), pennylane.devices.qutrit_mixed.sampling (delayed), pennylane.gradients.pulse_gradient (optional), pennylane.gradients.pulse_gradient_odegen (optional), pennylane.gradients.classical_jacobian (delayed, conditional), pennylane.gradients.fisher (delayed, conditional), pennylane.templates.subroutines.qsvt (delayed, optional), pennylane.ops.op_math.decompositions.unitary_decompositions (delayed, conditional), pennylane.ops.op_math.controlled (delayed), pennylane.ops.op_math.adjoint (delayed), pennylane.capture.make_plxpr (optional), pennylane.allocation (optional), pennylane.optimize.qng_qjit (optional)
missing module named 'cython.cimports' - imported by Cython.Plex.DFA (conditional), zmq.backend.cython._zmq (top-level)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pythran - imported by Cython.Build.Dependencies (optional), Cython.Compiler.Pythran (optional)
missing module named pygments.lexers.CppLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.CythonLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (top-level), IPython.core.oinspect (top-level), numba.core.annotations.pretty_annotate (delayed, optional), scrapy.utils.display (delayed)
missing module named _winreg - imported by appdirs (delayed, conditional), pygments.formatters.img (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named chardet - imported by pygments.lexer (delayed, conditional, optional), requests (optional), bs4.dammit (optional), feedparser.encodings (optional)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed, optional), Cython.Compiler.Annotate (delayed, optional), IPython.lib.display (delayed), IPython.core.oinspect (top-level), stack_data.core (delayed), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional), scrapy.utils.display (delayed)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.formatters.LatexFormatter - imported by pygments.formatters (delayed), IPython.lib.display (delayed)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named Cython.Parser - imported by Cython.Compiler.Main (delayed, conditional, optional)
missing module named pyximport.test - imported by pyximport (conditional), pyximport.pyxbuild (conditional)
missing module named 'IPython.config' - imported by IPython.core.history (conditional)
missing module named nbformat - imported by IPython.core.magics.basic (delayed), IPython.core.interactiveshell (delayed, conditional)
missing module named traitlets.config.Application - imported by traitlets.config (delayed, conditional), traitlets.log (delayed, conditional), ipykernel.kernelspec (top-level)
missing module named argcomplete - imported by traitlets.config.loader (delayed, optional), traitlets.config.argcomplete_config (optional)
missing module named numpydoc - imported by jedi.inference.docstrings (delayed), sklearn.utils._testing (delayed, optional)
missing module named collections.Mapping - imported by collections (optional), sortedcontainers.sorteddict (optional), parso.python.tree (optional), google.auth.jwt (optional), urllib3._collections (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), pytz.lazy (optional), html5lib._utils (optional), html5lib._trie._base (optional), patsy.constraint (optional), hyperlink._url (optional), peewee (optional), gevent.contextvars (optional), gevent.tests.test__local (optional)
missing module named exceptiongroup - imported by trio._util (conditional), trio._core._run (conditional), trio.testing._check_streams (conditional), _pytest.raises (conditional), _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), trio.testing._raises_group (conditional), trio._channel (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional)
missing module named 'curio.meta' - imported by sniffio._impl (delayed, conditional)
missing module named 'argcomplete.completers' - imported by _pytest._argcomplete (conditional, optional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional), simplejson.compat (conditional), imageio.core.util (delayed, conditional), gevent._compat (optional), eventlet.patcher (optional), eventlet.tpool (optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), socks (optional), peewee (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), sortedcontainers.sortedlist (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), sortedcontainers.sortedlist (conditional, optional), patsy.compat_ordereddict (optional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional), simplejson.compat (conditional, optional)
missing module named cPickle - imported by IPython.external.pickleshare (optional), pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by trio._core._run (delayed), torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named curio - imported by IPython.core.async_helpers (delayed)
missing module named docrepr - imported by IPython.core.interactiveshell (optional)
missing module named pexpect - imported by IPython.utils._process_posix (delayed, conditional), jupyter_client.ssh.tunnel (optional)
missing module named System - imported by IPython.utils._process_cli (top-level)
missing module named 'yapf.yapflib' - imported by IPython.terminal.interactiveshell (delayed)
missing module named yapf - imported by IPython.terminal.interactiveshell (delayed)
excluded module named black - imported by IPython.terminal.interactiveshell (delayed)
missing module named jupyter_ai - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named jupyter_ai_magics - imported by IPython.terminal.shortcuts.auto_suggest (delayed, optional)
missing module named prompt_toolkit.filters.vi_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.document (top-level), prompt_toolkit.key_binding.bindings.page_navigation (top-level), prompt_toolkit.widgets.toolbars (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named 'prompt_toolkit.key_binding.key_bindings.vi' - imported by prompt_toolkit.key_binding.vi_state (conditional)
missing module named 'backports.functools_lru_cache' - imported by wcwidth.wcwidth (optional)
missing module named prompt_toolkit.filters.is_done - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.base (top-level), prompt_toolkit.shortcuts.progress_bar.base (top-level), prompt_toolkit.shortcuts.prompt (top-level)
missing module named prompt_toolkit.filters.has_completions - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.menus (top-level), prompt_toolkit.widgets.toolbars (top-level), prompt_toolkit.widgets.dialogs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.vi_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.emacs_insert_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.containers (top-level), prompt_toolkit.key_binding.bindings.basic (top-level), prompt_toolkit.key_binding.bindings.emacs (top-level), IPython.terminal.shortcuts.filters (top-level)
missing module named prompt_toolkit.filters.is_searching - imported by prompt_toolkit.filters (top-level), prompt_toolkit.search (top-level), prompt_toolkit.key_binding.bindings.search (top-level), prompt_toolkit.key_binding.bindings.vi (top-level)
missing module named sip - imported by IPython.external.qt_loaders (delayed, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named prompt_toolkit.filters.vi_insert_multiple_mode - imported by prompt_toolkit.filters (top-level), prompt_toolkit.layout.processors (top-level)
missing module named 'astroid.node_classes' - imported by asttokens.astroid_compat (optional)
missing module named 'astroid.nodes' - imported by asttokens.astroid_compat (optional)
missing module named astroid - imported by asttokens.astroid_compat (optional), asttokens.util (optional)
missing module named pathlib2 - imported by IPython.external.pickleshare (optional)
missing module named netifaces - imported by jupyter_client.localinterfaces (delayed)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named httplib - imported by gevent.tests.test__socket_ssl (optional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), jupyter_client.ssh.tunnel (top-level), multitasking (top-level), gevent.tests.test__issue600 (top-level)
missing module named _import_wait - imported by gevent.tests.test__import_wait (optional)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
missing module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional), gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (optional)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), jieba (delayed), multitasking (top-level), gevent.testing.testrunner (top-level)
missing module named test.support - imported by test (optional), gevent.testing.monkey_test (optional), gevent.testing.support (delayed, conditional, optional)
missing module named __builtin__ - imported by gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named 'test.libregrtest' - imported by gevent.testing.resources (delayed, optional)
missing module named 'test.lock_tests' - imported by gevent.testing.monkey_test (optional)
missing module named objgraph - imported by torch.testing._internal.common_utils (delayed, conditional, optional), gevent.testing.leakcheck (optional)
missing module named mock - imported by gevent.testing (optional)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named zmq.backend.zmq_version_info - imported by zmq.backend (top-level), zmq.sugar.version (top-level)
missing module named zmq.backend.Frame - imported by zmq.backend (top-level), zmq.sugar.frame (top-level), zmq.sugar.tracker (top-level)
missing module named zmq.backend.Socket - imported by zmq.backend (top-level), zmq.sugar.socket (top-level)
missing module named zmq.backend.zmq_poll - imported by zmq.backend (top-level), zmq.sugar.poll (top-level)
missing module named pyczmq - imported by zmq.sugar.context (delayed)
missing module named zmq.backend.Context - imported by zmq.backend (top-level), zmq.sugar.context (top-level)
missing module named zmq.backend.proxy - imported by zmq.backend (top-level), zmq.sugar (top-level)
missing module named zmq.ZMQError - imported by zmq (delayed, optional), zmq.sugar.attrsettr (delayed, optional)
missing module named zmq.backend.zmq_errno - imported by zmq.backend (delayed), zmq.error (delayed, conditional)
missing module named zmq.backend.strerror - imported by zmq.backend (delayed), zmq.error (delayed)
missing module named zmq.zmq_version_info - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named zmq.zmq_version - imported by zmq (delayed, conditional), zmq.error (delayed, conditional)
missing module named _subprocess - imported by jupyter_client.launcher (delayed, conditional, optional), ipykernel.parentpoller (delayed, optional)
missing module named paramiko - imported by jupyter_client.ssh.tunnel (optional), fsspec.implementations.sftp (top-level)
missing module named xdrlib - imported by dill._objects (conditional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named diff - imported by dill._dill (delayed, conditional, optional)
missing module named dill.diff - imported by dill (delayed, conditional, optional), dill._dill (delayed, conditional, optional)
missing module named version - imported by dill (optional)
missing module named 'ipyparallel.serialize' - imported by ipykernel.ipkernel (delayed, optional), ipykernel.serialize (optional), ipykernel.pickleutil (optional)
missing module named ipykernel.get_connection_info - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.get_connection_file - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named ipykernel.connect_qtconsole - imported by ipykernel (top-level), ipykernel.zmqshell (top-level)
missing module named PySide6 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PySide2 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named PyQt5 - imported by ipykernel.eventloops (delayed, conditional, optional)
missing module named 'gi.repository' - imported by ipykernel.gui.gtk3embed (top-level)
missing module named gtk - imported by ipykernel.gui.gtkembed (top-level)
missing module named gobject - imported by ipykernel.gui.gtkembed (top-level)
missing module named wx - imported by ipykernel.eventloops (delayed), IPython.lib.guisupport (delayed)
missing module named ipyparallel - imported by ipykernel.zmqshell (delayed, conditional)
missing module named appnope - imported by ipykernel.ipkernel (delayed, conditional)
missing module named '_pydevd_bundle.pydevd_api' - imported by ipykernel.debugger (delayed)
missing module named '_pydevd_bundle.pydevd_suspended_frames' - imported by ipykernel.debugger (optional)
missing module named _pydevd_bundle - imported by debugpy._vendored.force_pydevd (top-level), ipykernel.debugger (optional)
missing module named pydevd_file_utils - imported by debugpy.server.api (top-level)
missing module named '_pydevd_bundle.pydevd_constants' - imported by debugpy.server.api (top-level)
missing module named pydevd - imported by debugpy._vendored.force_pydevd (top-level), debugpy.server.api (top-level)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), scipy._lib._array_api (delayed), pandas.core.dtypes.common (delayed, conditional, optional), sklearn.tree._classes (top-level), sklearn.ensemble._forest (top-level), sklearn.ensemble._gb (top-level), sklearn.ensemble._iforest (top-level), shap.explainers._linear (top-level), pennylane.math.quantum (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.sph_jn - imported by scipy.special (delayed, conditional, optional), sympy.functions.special.bessel (delayed, conditional, optional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.integrate.trapz - imported by scipy.integrate (delayed, optional), statsmodels.genmod.generalized_estimating_equations (delayed, optional)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.optimize.root_scalar - imported by scipy.optimize (top-level), scipy.stats._continuous_distns (top-level), scipy.stats._stats_py (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.optimize.brentq - imported by scipy.optimize (delayed), scipy.integrate._ivp.ivp (delayed), scipy.stats._binomtest (top-level), scipy.stats._odds_ratio (top-level), statsmodels.genmod.generalized_linear_model (delayed, conditional)
missing module named scipy.optimize.OptimizeResult - imported by scipy.optimize (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.ivp (top-level), scipy._lib.cobyqa.main (top-level), scipy._lib.cobyqa.problem (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.dogbox (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._lsq.bvls (top-level), scipy.optimize._spectral (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level), scipy.optimize._qap (top-level), scipy.optimize._direct_py (top-level)
missing module named scipy.optimize.minimize_scalar - imported by scipy.optimize (top-level), scipy.interpolate._bsplines (top-level), scipy.stats._multicomp (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.sparse.linalg.matrix_power - imported by scipy.sparse.linalg (delayed), scipy.sparse._matrix (delayed), pennylane.math.single_dispatch (delayed, optional)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.lil_matrix - imported by scipy.sparse (top-level), sklearn.manifold._locally_linear (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.dia_matrix - imported by scipy.sparse (top-level), sklearn.cluster._bicluster (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.sparray - imported by scipy.sparse (optional), sklearn.utils.fixes (optional), networkx.utils.backends (delayed)
missing module named scipy.sparse.coo_array - imported by scipy.sparse (top-level), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.vstack - imported by scipy.sparse (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level), slicer.slicer_internal (delayed, conditional)
missing module named scipy.sparse.bmat - imported by scipy.sparse (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level)
missing module named scipy.sparse.find - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level)
missing module named scipy.sparse.csr_matrix - imported by scipy.sparse (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), sklearn.utils._param_validation (top-level), sklearn.metrics.pairwise (top-level), sklearn.neighbors._base (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.ensemble._gb (top-level), pennylane.math.matrix_manipulation (top-level), pennylane.ops.qubit.matrix_ops (top-level), pennylane.ops.qubit.observables (top-level), pennylane.ops.qubit.qchem_ops (top-level), pennylane.ops.qubit.state_preparation (top-level), pennylane.devices.qubit.measure (top-level), pennylane.devices.qubit_mixed.measure (top-level), cirq.ops.linear_combinations (top-level), cirq.ops.projector (top-level), qiskit.quantum_info.operators.symplectic.base_pauli (delayed, conditional), qiskit.quantum_info.operators.symplectic.sparse_pauli_op (delayed, conditional), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.radau (top-level), scipy.linalg._sketches (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.io._harwell_boeing.hb (top-level), sklearn.cluster._spectral (top-level), sklearn.ensemble._gb (top-level), pennylane.math.quantum (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.coo_matrix - imported by scipy.sparse (top-level), scipy.integrate._bvp (top-level), scipy.optimize._numdiff (top-level), scipy.integrate._ivp.common (top-level), scipy.stats._crosstab (top-level), pandas.core.arrays.sparse.accessor (delayed), scipy.io.matlab._mio (delayed, conditional), scipy.io._fast_matrix_market (top-level), scipy.io._mmio (top-level), sklearn.metrics._classification (top-level), qiskit.transpiler.passes.layout.dense_layout (delayed), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), pennylane.math.matrix_manipulation (top-level), pennylane.ops.op_math.prod (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), sklearn.manifold._locally_linear (top-level), pennylane.math.matrix_manipulation (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level), scipy.io._harwell_boeing.hb (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level), pennylane.ops.qubit.state_preparation (top-level), pennylane.data.attributes.sparse_array (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), jieba (delayed), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), scipy._lib._util (delayed, conditional)
missing module named numpy.ComplexWarning - imported by numpy (conditional), sklearn.utils.fixes (conditional), scipy._lib._util (conditional)
missing module named numpy.AxisError - imported by numpy (conditional), scipy._lib._util (conditional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named norm - imported by nltk.translate.gale_church (optional)
missing module named nltk.corpus.WordNetCorpusReader - imported by nltk.corpus (top-level), nltk.translate.meteor_score (top-level)
missing module named pycrfsuite - imported by nltk.tag.crf (optional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), urllib3.packages.six.moves.urllib (top-level), urllib3.util.queue (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional), scrapy.utils._compression (optional), scrapy.downloadermiddlewares.httpcompression (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional), scrapy.utils._compression (optional), scrapy.downloadermiddlewares.httpcompression (optional)
missing module named urllib3_secure_extra - imported by urllib3 (optional)
missing module named collections.MutableMapping - imported by collections (optional), urllib3._collections (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional)
missing module named 'urllib3.packages.six.moves.urllib.parse' - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
missing module named Queue - imported by urllib3.util.queue (conditional), numba.testing.main (optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named UserDict - imported by simplejson.ordered_dict (top-level), pytz.lazy (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional), seaborn.external.docscrape (delayed, conditional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named xlsxwriter - imported by pandas.io.excel._xlsxwriter (delayed)
missing module named 'openpyxl.cell' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.styles' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.workbook' - imported by pandas.io.excel._openpyxl (delayed)
missing module named 'openpyxl.descriptors' - imported by pandas.io.excel._openpyxl (conditional)
missing module named openpyxl - imported by pandas.io.excel._openpyxl (delayed, conditional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named xlrd - imported by pandas.io.excel._xlrd (delayed, conditional), pandas.io.excel._base (delayed, conditional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named distributed - imported by fsspec.transaction (delayed), joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fastapi.responses (optional), fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named js - imported by fsspec.implementations.http_sync (delayed, optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by joblib._dask (optional), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional), fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named zstandard - imported by httpx._decoders (optional), fsspec.compression (optional), scrapy.utils._compression (top-level), scrapy.downloadermiddlewares.httpcompression (optional)
missing module named lz4 - imported by fsspec.compression (optional), joblib.compressor (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), seaborn.widgets (optional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named llvmlite.binding.parse_assembly - imported by llvmlite.binding (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level), numba.cpython.unicode (top-level), numba.np.arrayobj (top-level), numba.np.ufunc.wrappers (top-level), numba.cpython.old_mathimpl (top-level), numba.cpython.old_numbers (top-level), numba.cpython.new_mathimpl (top-level), numba.cpython.new_numbers (top-level)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), pandas.core._numba.extensions (delayed)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named coverage - imported by numba.misc.coverage_support (optional), numba.tests.support (optional)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional), torch._appdirs (delayed, conditional, optional), seaborn.external.appdirs (delayed, conditional, optional), appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional), torch.testing._internal.common_utils (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named 'PyQt6.QtDataVisualization' - imported by qtpy.QtDataVisualization (conditional, optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional), scrapy.utils.boto (delayed, optional)
missing module named array_api_compat - imported by sklearn.externals.array_api_extra._lib._utils._compat (optional)
missing module named cupy_backends - imported by sklearn.externals.array_api_compat.common._helpers (delayed)
missing module named sklearn.externals.array_api_compat.common.array_namespace - imported by sklearn.externals.array_api_compat.common (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level)
missing module named torch.outer - imported by torch (top-level), sklearn.externals.array_api_compat.torch.linalg (top-level)
missing module named 'cupy.linalg' - imported by sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named 'cupy.fft' - imported by sklearn.externals.array_api_compat.cupy.fft (top-level)
missing module named array_api_strict - imported by sklearn.utils._array_api (delayed, conditional, optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional), pennylane.concurrency.executors.external.dask (delayed, optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named numpy.byte_bounds - imported by numpy (delayed, optional), joblib._memmapping_reducer (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named 'bllipparser.RerankingParser' - imported by nltk.parse.bllip (optional)
missing module named bllipparser - imported by nltk.parse.bllip (optional)
missing module named nltk.induce_pcfg - imported by nltk (delayed), nltk.grammar (delayed)
missing module named nltk.Prover9 - imported by nltk (delayed), nltk.sem.glue (delayed)
missing module named nltk.word_tokenize - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.FreqDist - imported by nltk (delayed), nltk.classify.textcat (delayed)
missing module named nltk.nonterminals - imported by nltk (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Tree - imported by nltk (delayed), nltk.tree.tree (delayed), nltk.chunk.regexp (delayed)
missing module named nltk.ProbabilisticTree - imported by nltk (delayed), nltk.tree.tree (delayed)
missing module named nltk.Production - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.grammar (delayed)
missing module named nltk.Nonterminal - imported by nltk (delayed), nltk.draw.cfg (delayed)
missing module named nltk.CFG - imported by nltk (delayed), nltk.draw.cfg (delayed), nltk.parse.chart (delayed), nltk.parse.recursivedescent (delayed), nltk.parse.shiftreduce (delayed), nltk.grammar (delayed)
missing module named numpypy - imported by nltk (optional)
missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named bson - imported by tweepy.cache (delayed)
missing module named jwt - imported by redis.auth.token (delayed, optional), oauthlib.common (delayed), oauthlib.oauth2.rfc6749.clients.service_application (delayed)
missing module named 'jwt.algorithms' - imported by oauthlib.oauth1.rfc5849.signature (delayed, conditional)
missing module named imghdr - imported by tweepy.api (delayed, optional)
missing module named web3 - imported by atlas_alternative_data (optional), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level)
missing module named orjson - imported by fastapi.responses (optional), ccxt.base.exchange (optional), curl_cffi.requests.models (optional), frozendict.monkeypatch (delayed, optional)
missing module named apexpro - imported by ccxt.base.exchange (optional)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional), ccxt.static_dependencies.sympy.external.gmpy (delayed, optional)
missing module named ccxt.static_dependencies.msgpack._cmsgpack - imported by ccxt.static_dependencies.msgpack (conditional, optional)
missing module named '__pypy__.builders' - imported by msgpack.fallback (conditional, optional), ccxt.static_dependencies.msgpack.fallback (conditional, optional)
missing module named __pypy__ - imported by msgpack.fallback (conditional), ccxt.static_dependencies.msgpack.fallback (conditional)
missing module named axolotl_curve25519 - imported by ccxt.base.exchange (optional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named cchardet - imported by bs4.dammit (optional), feedparser.encodings (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named 'chardet.universaldetector' - imported by html5lib._inputstream (delayed, conditional, optional)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named librosa - imported by transformers.audio_utils (conditional), atlas_video_processor (optional), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level)
missing module named xgboost - imported by shap.utils._clustering (delayed), shap.explainers._tree (delayed, conditional), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level)
missing module named cvxpy - imported by atlas_quantum_optimizer (optional), pennylane.kernels.postprocessing (delayed, optional), qiskit.quantum_info.operators.measures (delayed), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), torch._functorch.partitioners (delayed), torch.fx.passes.graph_drawer (optional), qiskit.visualization.pass_manager_visualization (delayed)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional), autoray.lazy.draw (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.session (delayed, conditional), scrapy.utils.console (delayed, optional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named pandas._testing.makeDataFrame - imported by pandas._testing (optional), statsmodels.compat.pandas (optional)
missing module named 'pandas.util.testing' - imported by statsmodels.compat.pandas (optional)
missing module named patsy.DesignMatrix - imported by patsy (delayed), statsmodels.tools.data (delayed)
missing module named _abcoll - imported by patsy.compat_ordereddict (optional)
missing module named patsy.DesignInfo - imported by patsy (delayed), statsmodels.base.model (delayed), statsmodels.genmod.generalized_linear_model (delayed), statsmodels.base._constraints (delayed)
missing module named patsy.EvalEnvironment - imported by patsy (delayed, conditional), statsmodels.base.model (delayed, conditional)
missing module named patsy.dmatrix - imported by patsy (delayed, conditional), statsmodels.regression._prediction (delayed, conditional), statsmodels.graphics.regressionplots (top-level), statsmodels.base.model (delayed, conditional), statsmodels.base._prediction_inference (delayed, conditional)
missing module named patsy.NAAction - imported by patsy (top-level), statsmodels.formula.formulatools (top-level)
missing module named patsy.dmatrices - imported by patsy (delayed, conditional), statsmodels.base.data (delayed, conditional), statsmodels.formula.formulatools (top-level)
missing module named 'numpy.testing.utils' - imported by patsy.constraint (delayed, optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named catboost - imported by shap.explainers._tree (delayed, conditional)
missing module named pyspark - imported by shap.explainers._tree (optional)
missing module named 'tensorflow.keras' - imported by transformers.optimization_tf (optional), shap.explainers._gradient (delayed)
missing module named 'tensorflow.python' - imported by shap.explainers._deep.deep_tf (top-level), shap.explainers.tf_utils (delayed, conditional), pennylane.math.utils (delayed, conditional), pennylane.workflow.interfaces.tensorflow (top-level), torch.contrib._tensorboard_vis (optional)
missing module named shap._cext_gpu - imported by shap (optional), shap.explainers._gpu_tree (optional)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), uvicorn.supervisors.multiprocess (top-level)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1._hypothesis_plugin (optional)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'google.cloud.secretmanager' - imported by pydantic_settings.sources.providers.gcp (delayed, conditional, optional)
missing module named 'azure.keyvault' - imported by pydantic_settings.sources.providers.azure (delayed, conditional, optional)
missing module named 'azure.core' - imported by pydantic_settings.sources.providers.azure (delayed, conditional, optional)
missing module named azure - imported by pydantic_settings.sources.providers.azure (conditional)
missing module named mypy_boto3_secretsmanager - imported by pydantic_settings.sources.providers.aws (delayed, optional)
missing module named boto3 - imported by pydantic_settings.sources.providers.aws (delayed, optional)
missing module named websockets.speedups - imported by websockets.frames (optional), websockets.legacy.framing (optional)
missing module named websockets.Subprotocol - imported by websockets (conditional), openai.types.websocket_connection_options (conditional)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional), redis._parsers.base (conditional), redis.asyncio.connection (conditional), redis._parsers.hiredis (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level), uvicorn.workers (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named winloop - imported by aiodns (delayed, conditional, optional)
missing module named monotonic - imported by eventlet.hubs.hub (optional), eventlet (optional)
missing module named itimer - imported by eventlet.hubs.hub (conditional, optional)
missing module named psycopg2 - imported by eventlet.support.psycopg2_patcher (top-level), peewee (optional), sqlalchemy (top-level), sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named 'OpenSSL.tsafe' - imported by eventlet.green.OpenSSL.tsafe (top-level)
missing module named readability - imported by curl_cffi.requests.models (top-level)
missing module named markdownify - imported by curl_cffi.requests.models (top-level)
missing module named frozendict._frozendict - imported by frozendict (optional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional), yfinance.live (top-level)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional), yfinance.live (top-level)
missing module named MySQLdb - imported by peewee (optional), sqlalchemy (top-level)
missing module named pymysql - imported by peewee (optional)
missing module named psycopg - imported by peewee (optional), sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg2.extras' - imported by peewee (optional)
missing module named psycopg2cffi - imported by peewee (optional)
missing module named pysqlite2 - imported by peewee (optional), sqlalchemy (top-level)
missing module named pysqlite3 - imported by peewee (optional)
missing module named msgpack._cmsgpack - imported by msgpack (conditional, optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named tensorflow - imported by opt_einsum.backends.tensorflow (delayed, conditional), huggingface_hub.keras_mixin (conditional, optional), huggingface_hub.serialization._tensorflow (delayed, conditional), transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.image_transforms (conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_tf_encoder_decoder (top-level), transformers.modeling_tf_outputs (top-level), transformers.modeling_tf_utils (top-level), transformers.activations_tf (top-level), transformers.tf_utils (top-level), safetensors.tensorflow (top-level), transformers.trainer_utils (delayed, conditional), transformers.modelcard (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.onnx.convert (delayed), transformers.models.roformer.modeling_roformer (delayed, optional), transformers.models.roformer.modeling_tf_roformer (top-level), transformers.generation.tf_logits_process (top-level), transformers.generation.tf_utils (top-level), transformers.models.bert.modeling_bert (delayed, optional), transformers.models.bert.modeling_tf_bert (top-level), transformers.models.bert.tokenization_bert_tf (top-level), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.glue (conditional), transformers.data.processors.squad (conditional), transformers.models.albert.modeling_albert (delayed, optional), transformers.models.albert.modeling_tf_albert (top-level), transformers.models.bart.modeling_tf_bart (top-level), transformers.models.big_bird.modeling_big_bird (delayed, optional), transformers.models.blenderbot_small.modeling_tf_blenderbot_small (top-level), transformers.models.blenderbot.modeling_tf_blenderbot (top-level), transformers.models.blip.modeling_tf_blip (top-level), transformers.models.blip.modeling_tf_blip_text (top-level), transformers.models.camembert.modeling_tf_camembert (top-level), transformers.models.canine.modeling_canine (delayed, optional), transformers.models.clip.modeling_tf_clip (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.convbert.modeling_convbert (delayed, optional), transformers.models.convbert.modeling_tf_convbert (top-level), transformers.models.convnext.modeling_tf_convnext (top-level), transformers.models.convnextv2.modeling_tf_convnextv2 (top-level), transformers.models.ctrl.modeling_tf_ctrl (top-level), transformers.models.cvt.modeling_tf_cvt (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.modeling_tf_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.efficientformer.modeling_tf_efficientformer (top-level), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.deprecated.nezha.modeling_nezha (delayed, optional), transformers.models.deprecated.qdqbert.modeling_qdqbert (delayed, optional), transformers.models.deprecated.realm.modeling_realm (delayed, optional), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl (top-level), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (delayed, optional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.distilbert.modeling_tf_distilbert (top-level), transformers.models.dpr.modeling_tf_dpr (top-level), transformers.models.electra.modeling_electra (delayed, optional), transformers.models.electra.modeling_tf_electra (top-level), transformers.models.esm.modeling_tf_esm (top-level), transformers.models.flaubert.modeling_tf_flaubert (top-level), transformers.models.funnel.modeling_funnel (delayed, optional), transformers.models.funnel.modeling_tf_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (delayed, optional), transformers.models.gpt2.modeling_tf_gpt2 (top-level), transformers.models.gpt2.tokenization_gpt2_tf (top-level), transformers.models.gpt_neo.modeling_gpt_neo (delayed, optional), transformers.models.gptj.modeling_tf_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.groupvit.modeling_tf_groupvit (top-level), transformers.models.hubert.modeling_tf_hubert (top-level), transformers.models.idefics.modeling_tf_idefics (top-level), transformers.models.idefics.perceiver_tf (top-level), transformers.models.idefics.vision_tf (top-level), transformers.models.idefics.processing_idefics (conditional), transformers.models.imagegpt.modeling_imagegpt (delayed, optional), transformers.models.layoutlm.modeling_tf_layoutlm (top-level), transformers.models.layoutlmv3.modeling_tf_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_tf_xlm_roberta (top-level), transformers.models.led.modeling_tf_led (top-level), transformers.models.longformer.modeling_tf_longformer (top-level), transformers.models.lxmert.modeling_lxmert (delayed, optional), transformers.models.lxmert.modeling_tf_lxmert (top-level), transformers.models.marian.modeling_tf_marian (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.mbart.modeling_tf_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (delayed, optional), transformers.models.mistral.modeling_tf_mistral (top-level), transformers.models.mobilebert.modeling_mobilebert (delayed, optional), transformers.models.mobilebert.modeling_tf_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (delayed, optional), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (delayed, optional), transformers.models.mobilevit.modeling_tf_mobilevit (top-level), transformers.models.mpnet.modeling_tf_mpnet (top-level), transformers.models.t5.modeling_t5 (delayed, optional), transformers.models.t5.modeling_tf_t5 (top-level), transformers.models.mt5.modeling_mt5 (delayed, optional), transformers.models.openai.modeling_tf_openai (top-level), transformers.models.opt.modeling_tf_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_tf_pegasus (top-level), transformers.models.rag.modeling_tf_rag (top-level), transformers.models.regnet.modeling_tf_regnet (top-level), transformers.models.rembert.modeling_rembert (delayed, optional), transformers.models.rembert.modeling_tf_rembert (top-level), transformers.models.resnet.modeling_tf_resnet (top-level), transformers.models.roberta.modeling_tf_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_tf_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (delayed, optional), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_tf_sam (top-level), transformers.models.sam.processing_sam (conditional), transformers.models.segformer.modeling_tf_segformer (top-level), transformers.models.speech_to_text.modeling_tf_speech_to_text (top-level), transformers.models.swiftformer.modeling_tf_swiftformer (top-level), transformers.models.tapas.modeling_tapas (delayed, optional), transformers.models.tapas.modeling_tf_tapas (top-level), transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.wav2vec2.modeling_tf_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_tf_whisper (top-level), transformers.models.xglm.modeling_tf_xglm (top-level), transformers.models.xlm.modeling_tf_xlm (top-level), transformers.models.xlnet.modeling_tf_xlnet (top-level), transformers.models.xlnet.modeling_xlnet (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.pipelines.base (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text2text_generation (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.token_classification (conditional), transformers.pipelines (conditional), transformers.training_args_tf (conditional), transformers.keras_callbacks (top-level), transformers.optimization_tf (top-level), shap.models._teacher_forcing (delayed, conditional), shap.models._text_generation (delayed, conditional, optional), shap.models._topk_lm (delayed, conditional), shap.explainers._deep.deep_tf (top-level), shap.explainers.tf_utils (delayed, conditional), shap.explainers._gradient (delayed, conditional, optional), shap.explainers._kernel (delayed), autoray.compiler (delayed), pennylane.math.is_independent (delayed, conditional), pennylane.math.utils (delayed, conditional), pennylane.math.single_dispatch (delayed), pennylane.math.multi_dispatch (delayed, conditional), pennylane.math.fidelity (delayed), pennylane.math.grad (delayed, conditional), pennylane.typing (delayed, conditional), pennylane.ops.qubit.special_unitary (delayed, conditional), pennylane.transforms.core.cotransform_cache (delayed), pennylane.workflow.resolution (delayed, optional), pennylane.workflow.interfaces.tensorflow_autograph (top-level), pennylane.workflow.interfaces.tensorflow (top-level), pennylane.devices.qubit_mixed.measure (delayed, conditional), pennylane.gradients.classical_jacobian (delayed, conditional), pennylane.gradients.fisher (delayed), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.granite_speech.feature_extraction_granite_speech (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional)
missing module named 'torchvision.io' - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (conditional), transformers.models.yolos.image_processing_yolos_fast (conditional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'triton.runtime' - imported by torch.utils._triton (delayed), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.runtime.runtime_utils (delayed, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.select_algorithm (delayed, optional), torch._inductor.fx_passes.reinplace (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional), torch._inductor.ir (delayed), torch._inductor.codecache (delayed, conditional), torch._library.triton (delayed), torch._inductor.utils (delayed)
missing module named 'triton.compiler' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.triton (delayed), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.async_compile (delayed, optional), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional), torch._inductor.utils (delayed)
missing module named 'triton.backends' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (conditional), torch._inductor.runtime.triton_compat (conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._inductor.utils (delayed)
missing module named 'triton.language' - imported by torch.utils._triton (delayed, conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed), torch._inductor.codegen.triton_split_scan (delayed), transformers.integrations.finegrained_fp8 (conditional), torch.sparse._triton_ops (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'triton.tools' - imported by torch.utils._triton (delayed, conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed, conditional), torch._dynamo.variables.builder (delayed, conditional)
missing module named triton - imported by torch.utils._triton (delayed, optional), torch._inductor.runtime.hints (conditional), torch._dynamo.logging (conditional, optional), torch._inductor.runtime.triton_compat (conditional, optional), torch._inductor.codegen.wrapper (delayed, conditional), transformers.integrations.finegrained_fp8 (conditional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm (optional), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.ir (optional), torch._functorch._aot_autograd.autograd_cache (delayed, conditional), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch.testing._internal.inductor_utils (conditional), torch.testing._internal.triton_utils (conditional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.cuda._memory_viz (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named optree - imported by torch.utils._cxx_pytree (top-level), torch._dynamo.polyfills.pytree (conditional)
missing module named 'hypothesis.strategies' - imported by pydantic.v1._hypothesis_plugin (top-level), torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named pytest_subtests - imported by torch.testing._internal.opinfo.core (delayed, conditional, optional)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.device_mesh (conditional), torch.distributed.distributed_c10d (top-level), torch._inductor.codegen.wrapper (delayed, optional), torch.distributed.tensor._collective_utils (top-level), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.testing._internal.distributed.fake_pg (top-level), torch._dynamo.variables.distributed (delayed), torch.distributed._symmetric_memory (top-level), torch.distributed.constants (top-level), torch.distributed._tools.fake_collectives (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.common_distributed (top-level), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named torchvision - imported by transformers.image_utils (conditional), transformers.video_utils (conditional), torch.testing._internal.common_quantization (optional), torch.testing._internal.distributed.distributed_test (optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level), transformers.quantizers.base (conditional)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named torch.ao.quantization.QConfigMapping - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.custom_config (top-level), torch.ao.ns.fx.n_shadows_utils (top-level), torch.ao.ns.fx.qconfig_multi_mapping (top-level), torch.ao.ns._numeric_suite_fx (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.ao.quantization.pt2e.prepare (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QConfig - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.qconfig_mapping_utils (top-level), torch.ao.quantization.fx.lstm_utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named torch.ao.quantization.QuantType - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level), torch.testing._internal.common_quantization (top-level)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.distributed._symmetric_memory (top-level), torch.autograd (top-level), torch.testing._internal.common_distributed (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch.fx.passes.utils.common (top-level), torch.distributed.nn.api.remote_module (top-level), torch._dynamo.mutation_guard (top-level), torch.fx.experimental.proxy_tensor (top-level), pennylane.qnn.torch (optional)
missing module named onnx - imported by torch.utils.tensorboard._onnx_graph (delayed), torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._building (conditional), transformers.onnx.convert (delayed), torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (conditional), torch.onnx._internal._exporter_legacy (delayed, optional), torch.onnx.verification (delayed, optional)
missing module named onnxruntime - imported by torch.onnx._internal.exporter._onnx_program (delayed, conditional), transformers.onnx.convert (delayed, optional), torch.onnx._internal.onnxruntime (delayed, conditional), torch.onnx._internal._exporter_legacy (conditional), torch.onnx.verification (delayed, optional)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx.utils (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (delayed, conditional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named onnxscript - imported by torch.onnx._internal._lazy_import (conditional), torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._dispatching (top-level), torch.onnx._internal.exporter._schemas (top-level), torch.onnx._internal.exporter._torchlib._torchlib_registry (top-level), torch.onnx._internal.exporter._torchlib._tensor_typing (top-level), torch.onnx._internal.exporter._building (top-level), torch.onnx._internal.exporter._tensors (top-level), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.exporter._verification (conditional), torch.onnx._internal.exporter._reporting (conditional), torch.onnx._internal._exporter_legacy (delayed, conditional, optional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter._ir_passes (delayed, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named bitsandbytes - imported by transformers.utils.import_utils (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional), transformers.models.rwkv.modeling_rwkv (delayed)
missing module named intel_extension_for_pytorch - imported by transformers.utils.import_utils (delayed, conditional)
missing module named 'habana_frameworks.torch' - imported by transformers.utils.import_utils (delayed)
missing module named habana_frameworks - imported by transformers.utils.import_utils (delayed, conditional)
missing module named torch_musa - imported by transformers.utils.import_utils (delayed)
missing module named torch_mlu - imported by transformers.utils.import_utils (delayed)
missing module named torch_npu - imported by transformers.utils.import_utils (delayed), transformers.integrations.npu_flash_attention (conditional)
missing module named torch_xla - imported by torch.distributed.tensor._api (delayed, conditional, optional), huggingface_hub.serialization._torch (delayed, conditional), transformers.utils.import_utils (delayed), transformers.pytorch_utils (delayed, conditional), transformers.trainer (conditional), torch._tensor (delayed, conditional)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named 'google.colab' - imported by huggingface_hub.utils._auth (delayed, optional)
missing module named hf_xet - imported by huggingface_hub.file_download (delayed, optional), huggingface_hub._commit_api (delayed)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named mlx - imported by transformers.utils.generic (delayed)
missing module named 'jax.core' - imported by transformers.utils.generic (delayed, conditional), pennylane.math.single_dispatch (delayed)
missing module named transformers.AutoConfig - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named transformers.AutoBackbone - imported by transformers (delayed), transformers.utils.backbone_utils (delayed)
missing module named 'torchvision.transforms' - imported by transformers.image_processing_utils_fast (conditional), transformers.video_processing_utils (conditional), transformers.models.llama4.image_processing_llama4_fast (conditional), transformers.models.beit.image_processing_beit_fast (top-level), transformers.models.bridgetower.image_processing_bridgetower_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (conditional), transformers.models.convnext.image_processing_convnext_fast (conditional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (conditional), transformers.models.depth_pro.image_processing_depth_pro_fast (conditional), transformers.models.detr.image_processing_detr_fast (conditional), transformers.models.donut.image_processing_donut_fast (conditional), transformers.models.dpt.image_processing_dpt_fast (conditional), transformers.models.efficientnet.image_processing_efficientnet_fast (conditional), transformers.models.flava.image_processing_flava_fast (conditional), transformers.models.gemma3.image_processing_gemma3_fast (conditional), transformers.models.got_ocr2.image_processing_got_ocr2_fast (conditional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (conditional), transformers.models.idefics2.image_processing_idefics2_fast (conditional), transformers.models.idefics3.image_processing_idefics3_fast (conditional), transformers.models.instructblipvideo.video_processing_instructblipvideo (conditional), transformers.models.internvl.video_processing_internvl (conditional), transformers.models.layoutlmv2.image_processing_layoutlmv2_fast (conditional), transformers.models.layoutlmv3.image_processing_layoutlmv3_fast (conditional), transformers.models.levit.image_processing_levit_fast (conditional), transformers.models.llava.image_processing_llava_fast (conditional), transformers.models.llava_next.image_processing_llava_next_fast (conditional), transformers.models.llava_onevision.image_processing_llava_onevision_fast (conditional), transformers.models.perceiver.image_processing_perceiver_fast (conditional), transformers.models.phi4_multimodal.image_processing_phi4_multimodal_fast (conditional), transformers.models.pixtral.image_processing_pixtral_fast (conditional), transformers.models.poolformer.image_processing_poolformer_fast (conditional), transformers.models.qwen2_vl.image_processing_qwen2_vl_fast (conditional), transformers.models.rt_detr.image_processing_rt_detr_fast (conditional), transformers.models.siglip2.image_processing_siglip2_fast (conditional), transformers.models.smolvlm.image_processing_smolvlm_fast (conditional), transformers.models.smolvlm.video_processing_smolvlm (conditional), transformers.models.swin2sr.image_processing_swin2sr_fast (conditional), transformers.models.vilt.image_processing_vilt_fast (conditional), transformers.models.vitmatte.image_processing_vitmatte_fast (conditional), transformers.models.yolos.image_processing_yolos_fast (conditional), transformers.models.zoedepth.image_processing_zoedepth_fast (conditional), atlas_image_analyzer (optional)
missing module named yt_dlp - imported by transformers.video_utils (delayed, conditional)
missing module named torchcodec - imported by transformers.video_utils (delayed)
missing module named av - imported by transformers.video_utils (delayed), transformers.pipelines.video_classification (conditional), imageio.plugins.pyav (top-level)
missing module named decord - imported by transformers.video_utils (delayed)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named apex - imported by transformers.training_args (delayed, conditional, optional), transformers.trainer (delayed, conditional)
missing module named 'torch_xla.distributed' - imported by torch.distributed.tensor._api (delayed, conditional, optional), transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.core' - imported by huggingface_hub.serialization._torch (delayed, conditional, optional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), transformers.trainer (conditional), torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named smdistributed - imported by transformers.trainer_pt_utils (conditional)
missing module named 'torch_xla.runtime' - imported by transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (conditional), transformers.trainer (conditional)
missing module named 'accelerate.state' - imported by transformers.training_args (conditional), transformers.trainer (conditional)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named datasets - imported by transformers.modeling_tf_utils (delayed), transformers.modelcard (delayed, conditional), transformers.trainer (conditional), transformers.models.rag.retrieval_rag (conditional), transformers.trainer_seq2seq (conditional)
missing module named keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional), pennylane.liealg.structure_constants (delayed, conditional)
missing module named tf_keras - imported by huggingface_hub.keras_mixin (conditional, optional), transformers.activations_tf (optional), transformers.modeling_tf_utils (optional)
missing module named h5py - imported by transformers.modeling_tf_utils (top-level), pennylane.qchem.vibrational.christiansen_utils (optional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.modeling_utils (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named deepspeed - imported by transformers.integrations.deepspeed (delayed, conditional), transformers.modeling_utils (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional)
missing module named 'smdistributed.modelparallel' - imported by transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named kernels - imported by transformers.integrations.hub_kernels (optional), transformers.modeling_utils (delayed, conditional)
missing module named 'accelerate.utils' - imported by transformers.trainer_utils (delayed, conditional), transformers.training_args (delayed, conditional), transformers.integrations.deepspeed (delayed, conditional), transformers.loss.loss_for_object_detection (conditional), transformers.quantizers.quantizer_bnb_4bit (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.quantizers.quantizer_quark (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional), transformers.integrations.peft (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional)
missing module named 'accelerate.hooks' - imported by transformers.generation.utils (conditional), transformers.quantizers.quantizer_hqq (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitsandbytes (conditional)
missing module named accelerate - imported by transformers.integrations.aqlm (delayed), transformers.loss.loss_for_object_detection (conditional), transformers.quantizers.base (delayed), transformers.integrations.finegrained_fp8 (conditional), transformers.modeling_utils (conditional), transformers.integrations.bitnet (conditional), transformers.integrations.bitsandbytes (conditional), transformers.integrations.eetq (conditional), transformers.integrations.fbgemm_fp8 (conditional), transformers.integrations.higgs (delayed), transformers.trainer (conditional), transformers.integrations.peft (conditional), transformers.integrations.quanto (delayed), transformers.integrations.spqr (delayed, conditional), transformers.integrations.vptq (top-level), transformers.models.bark.modeling_bark (delayed, conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional)
missing module named 'quark.torch' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_quark (delayed)
missing module named quark - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named 'torchao.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'torchao.dtypes' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed)
missing module named 'torchao.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'compressed_tensors.config' - imported by transformers.utils.quantization_config (delayed)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed, conditional)
missing module named einops - imported by transformers.integrations.npu_flash_attention (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level), torch._dynamo.decorators (delayed, conditional)
missing module named flash_attn_interface - imported by transformers.modeling_flash_attention_utils (conditional)
missing module named 'flash_attn.layers' - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.bert_padding' - imported by transformers.modeling_flash_attention_utils (conditional)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional), transformers.integrations.flash_paged (conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named 'flute.utils' - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'auto_round.inference' - imported by transformers.quantizers.quantizer_auto_round (delayed, conditional)
missing module named auto_round - imported by transformers.quantizers.quantizer_auto_round (delayed)
missing module named torch.nn.MSELoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.loss.loss_utils (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level)
missing module named torch.nn.BCEWithLogitsLoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.loss.loss_utils (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level)
missing module named 'opentelemetry.trace' - imported by transformers.utils.metrics (optional)
missing module named 'opentelemetry.sdk' - imported by transformers.utils.metrics (optional)
missing module named 'opentelemetry.exporter' - imported by transformers.utils.metrics (optional)
missing module named opentelemetry - imported by transformers.utils.metrics (optional)
missing module named 'deepspeed.utils' - imported by transformers.integrations.deepspeed (delayed)
missing module named spqr_quant - imported by transformers.integrations.spqr (delayed, conditional)
missing module named 'peft.tuners' - imported by transformers.integrations.peft (delayed), transformers.models.data2vec.modeling_data2vec_audio (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional)
missing module named 'peft.utils' - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named peft - imported by transformers.trainer (delayed, conditional), transformers.integrations.peft (delayed)
missing module named swanlab - imported by transformers.integrations.integration_utils (delayed)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'dvclive.plots' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named dvclive - imported by transformers.integrations.integration_utils (delayed)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named clearml - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
excluded module named mlflow - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'torch.distributed._tensor.experimental' - imported by transformers.trainer (delayed, conditional)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.optim' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named lomo_optim - imported by transformers.trainer (delayed, conditional)
missing module named apollo_torch - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named torchdistx - imported by transformers.trainer (delayed, conditional, optional), torch.distributed.fsdp._init_utils (optional)
missing module named 'bitsandbytes.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.amp' - imported by transformers.trainer (delayed, conditional, optional)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named 'accelerate.data_loader' - imported by transformers.trainer (conditional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named tiktoken - imported by transformers.convert_slow_tokenizer (delayed, optional)
missing module named torch.nn.CrossEntropyLoss - imported by torch.nn (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hgnet_v2.modeling_hgnet_v2 (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip2.modeling_siglip2 (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.models.zamba2.modeling_zamba2 (top-level)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), autoray.autoray (delayed), pennylane.devices.qubit.apply_operation (delayed, conditional), pennylane.devices.qubit.sampling (delayed), pennylane.devices.qubit.simulate (delayed, conditional)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.generation.flax_utils (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level)
missing module named transformers.AutoTokenizer - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional), transformers.models.deprecated.realm.retrieval_realm (top-level), transformers.models.mgp_str.processing_mgp_str (top-level), atlas_video_processor (optional), atlas_data_fusion (optional)
missing module named transformers.AutoProcessor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named transformers.AutoFeatureExtractor - imported by transformers (conditional), transformers.onnx.utils (delayed, conditional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed, conditional), transformers.models.llama.tokenization_llama (top-level), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.bert_japanese.tokenization_bert_japanese (conditional), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.code_llama.tokenization_code_llama (top-level), transformers.models.cpm.tokenization_cpm (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.deprecated.ernie_m.tokenization_ernie_m (top-level), transformers.models.deprecated.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.gemma.tokenization_gemma (top-level), transformers.models.siglip.tokenization_siglip (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.seamless_m4t.tokenization_seamless_m4t (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.speecht5.tokenization_speecht5 (top-level), transformers.models.udop.tokenization_udop (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlnet.tokenization_xlnet (top-level)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named 'tensorboard.summary' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard (top-level)
missing module named moviepy.editor - imported by moviepy (delayed, optional), torch.utils.tensorboard.summary (delayed, optional)
missing module named 'zarr.core' - imported by tifffile.zarr (delayed, conditional, optional)
missing module named 'zarr.abc' - imported by tifffile.zarr (optional)
missing module named zarr - imported by tifffile.zarr (top-level)
missing module named _imagecodecs - imported by tifffile.tifffile (delayed, conditional, optional)
missing module named imagecodecs - imported by imageio.plugins._tifffile (delayed, conditional, optional), tifffile.tifffile (optional)
missing module named compression - imported by tifffile._imagecodecs (delayed, optional)
missing module named SimpleITK - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named osgeo - imported by imageio.plugins.gdal (delayed, optional)
missing module named astropy - imported by imageio.plugins.fits (delayed, optional)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named 'tensorboard.plugins' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.compat' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard._onnx_graph (top-level), torch.utils.tensorboard._pytorch_graph (top-level), torch.utils.tensorboard._proto_graph (top-level), torch.utils.tensorboard.summary (top-level)
excluded module named tensorboard - imported by torch.utils.tensorboard (top-level)
missing module named 'wandb.env' - imported by transformers.integrations.integration_utils (delayed, conditional)
excluded module named wandb - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named comet_ml - imported by transformers.integrations.integration_utils (delayed, conditional, optional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.tune' - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level), pennylane.devices.qubit.apply_operation (delayed, conditional)
missing module named 'tensorflow.compiler' - imported by transformers.generation.tf_utils (top-level), transformers.models.t5.modeling_tf_t5 (top-level)
missing module named torch.nn.BCELoss - imported by torch.nn (top-level), transformers.generation.watermarking (top-level)
missing module named torchao - imported by transformers.modeling_utils (conditional), transformers.quantizers.quantizer_torchao (delayed, conditional)
missing module named 'mlx.core' - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named transformers.TFEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.encoder_decoder.modeling_encoder_decoder (delayed, conditional)
missing module named transformers.models.auto.TFAutoModelForTokenClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSequenceClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSeq2SeqLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForSemanticSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForQuestionAnswering - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForMultipleChoice - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForMaskedLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModelForCausalLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.TFAutoModel - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForVision2Seq - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForTokenClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSpeechSeq2Seq - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSequenceClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForSeq2SeqLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level)
missing module named transformers.models.auto.AutoModelForSemanticSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForQuestionAnswering - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForObjectDetection - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMultipleChoice - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMaskedLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForMaskedImageModeling - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForImageSegmentation - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForImageClassification - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional)
missing module named transformers.models.auto.AutoModelForCausalLM - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level)
missing module named transformers.models.auto.AutoModel - imported by transformers.models.auto (conditional), transformers.onnx.features (conditional), transformers.models.aria.modeling_aria (top-level), transformers.models.aya_vision.modeling_aya_vision (top-level), transformers.models.bark.modeling_bark (top-level), transformers.models.csm.modeling_csm (top-level), transformers.models.depth_pro.modeling_depth_pro (top-level), transformers.models.gemma3.modeling_gemma3 (top-level), transformers.models.got_ocr2.modeling_got_ocr2 (top-level), transformers.models.granite_speech.modeling_granite_speech (top-level), transformers.models.grounding_dino.modeling_grounding_dino (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.internvl.modeling_internvl (top-level), transformers.models.janus.modeling_janus (top-level), transformers.models.kyutai_speech_to_text.modeling_kyutai_speech_to_text (top-level), transformers.models.llava.modeling_llava (top-level), transformers.models.llava_next.modeling_llava_next (top-level), transformers.models.llava_next_video.modeling_llava_next_video (top-level), transformers.models.llava_onevision.modeling_llava_onevision (top-level), transformers.models.mistral3.modeling_mistral3 (top-level), transformers.models.omdet_turbo.modeling_omdet_turbo (top-level), transformers.models.paligemma.modeling_paligemma (top-level), transformers.models.qwen2_audio.modeling_qwen2_audio (top-level), transformers.models.smolvlm.modeling_smolvlm (top-level), transformers.models.video_llava.modeling_video_llava (top-level), transformers.models.vipllava.modeling_vipllava (top-level)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_h1.modeling_falcon_h1 (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_h1.modeling_falcon_h1 (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.granitemoehybrid.modeling_granitemoehybrid (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional), transformers.models.zamba2.modeling_zamba2 (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named pycocotools - imported by transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr_fast (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino_fast (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.yolos.image_processing_yolos_fast (delayed, optional)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named torch.nn.LogSoftmax - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named torch.nn.KLDivLoss - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named transformers.TFVisionEncoderDecoderModel - imported by transformers (delayed, conditional), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (delayed, conditional)
missing module named transformers.UdopConfig - imported by transformers (top-level), transformers.models.udop.modeling_udop (top-level)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named timm - imported by transformers.models.timm_backbone.modeling_timm_backbone (conditional), transformers.models.conditional_detr.modeling_conditional_detr (conditional), transformers.models.deformable_detr.modeling_deformable_detr (conditional), transformers.models.detr.modeling_detr (conditional), transformers.models.grounding_dino.modeling_grounding_dino (conditional), transformers.models.table_transformer.modeling_table_transformer (conditional), transformers.models.timm_wrapper.modeling_timm_wrapper (conditional)
missing module named 'timm.data' - imported by transformers.models.timm_wrapper.configuration_timm_wrapper (conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named transformers.models.t5gemma.modeling_encdecgemma2 - imported by transformers.models.t5gemma (conditional)
missing module named transformers.models.t5gemma.configuration_encdecgemma2 - imported by transformers.models.t5gemma (conditional)
missing module named 'apex.normalization' - imported by transformers.models.longt5.modeling_longt5 (optional), transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named torch.nn.L1Loss - imported by torch.nn (top-level), transformers.models.speecht5.modeling_speecht5 (top-level)
missing module named 'torchaudio.compliance' - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named num2words - imported by transformers.models.smolvlm.processing_smolvlm (conditional)
missing module named torch.nn.LayerNorm - imported by torch.nn (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.sew_d.modeling_sew_d (top-level)
missing module named 'tensorflow.experimental' - imported by transformers.models.sam.image_processing_sam (conditional)
missing module named 'torchvision.ops' - imported by transformers.models.deprecated.deta.image_processing_deta (conditional), transformers.models.deprecated.deta.modeling_deta (conditional), transformers.models.omdet_turbo.processing_omdet_turbo (conditional), transformers.models.sam.image_processing_sam (conditional), torch._inductor.utils (delayed, optional)
missing module named transformers.RegNetConfig - imported by transformers (top-level), transformers.models.regnet.modeling_flax_regnet (top-level)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named spacy - imported by transformers.models.openai.tokenization_openai (delayed, optional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional)
missing module named Levenshtein - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named transformers.models.mistral3.processing_mistral3 - imported by transformers.models.mistral3 (conditional)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named torch.nn.SmoothL1Loss - imported by torch.nn (top-level), transformers.models.lxmert.modeling_lxmert (top-level)
missing module named pytesseract - imported by transformers.models.layoutlmv2.image_processing_layoutlmv2 (conditional), transformers.models.layoutlmv3.image_processing_layoutlmv3 (conditional), transformers.pipelines.document_question_answering (conditional)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (conditional)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named 'natten.functional' - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named soundfile - imported by transformers.models.csm.processing_csm (conditional), transformers.models.dia.processing_dia (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'tensorflow.compat' - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named xformers - imported by transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named natten - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named transformers.AutoModelForImageTextToText - imported by transformers (top-level), transformers.models.colpali.modeling_colpali (top-level), transformers.models.colqwen2.modeling_colqwen2 (top-level)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named 'mcp.client' - imported by huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named mcp - imported by huggingface_hub.inference._mcp.utils (conditional), huggingface_hub.inference._mcp.mcp_client (delayed, conditional)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named gradio - imported by huggingface_hub._webhooks_server (delayed, conditional)
missing module named 'authlib.integrations' - imported by huggingface_hub._oauth (delayed, optional)
missing module named authlib - imported by huggingface_hub._oauth (delayed, optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named 'onnxscript.ir' - imported by torch.onnx._internal.exporter._core (top-level), torch.onnx._internal.exporter._torchlib.ops.hop (delayed), torch.onnx._internal.exporter._building (top-level)
missing module named 'onnxscript.onnx_opset' - imported by torch.onnx._internal.exporter._torchlib.ops.core (top-level)
missing module named pyinstrument - imported by torch.onnx._internal.exporter._core (delayed, conditional)
missing module named 'onnxscript.evaluator' - imported by torch.onnx._internal.exporter._core (top-level)
missing module named 'onnxscript._framework_apis' - imported by torch.onnx._internal._lazy_import (conditional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), qiskit.transpiler.passes.optimization._gate_extension (conditional), qiskit.transpiler.passes.optimization.hoare_opt (delayed), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.distributed.ReduceOp - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level)
missing module named torch.distributed.group - imported by torch.distributed (top-level), torch.distributed.nn.functional (top-level), torch.distributed.algorithms.model_averaging.utils (top-level)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (optional), torch.distributed.elastic.rendezvous.etcd_store (optional), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (optional), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pulp - imported by torch.distributed._tools.sac_ilp (optional)
missing module named pwlf - imported by torch.distributed._tools.sac_estimator (delayed, optional)
missing module named amdsmi - imported by torch.cuda (conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named pynvml - imported by torch.cuda (delayed, conditional, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named torch.ao.quantization.QConfigAny - imported by torch.ao.quantization (top-level), torch.ao.quantization.fx.utils (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._higher_order_ops.cond (top-level), torch._subclasses.fake_tensor (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named fbscribelogger - imported by torch._logging.scribe (optional)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'torch._inductor.fb' - imported by torch._inductor.remote_cache (delayed, conditional, optional), torch._dynamo.pgo (delayed, optional), torch._inductor.cpp_builder (conditional), torch._inductor.compile_fx (conditional), torch._inductor.graph (conditional), torch._functorch._aot_autograd.autograd_cache (delayed, optional), torch._inductor.runtime.autotune_cache (delayed, optional), torch._inductor.codecache (conditional), torch._inductor.utils (delayed, optional), torch._dynamo.utils (delayed, conditional, optional)
missing module named 'triton.testing' - imported by torch._inductor.runtime.benchmarking (delayed, optional), torch._inductor.utils (delayed), torch._utils_internal (delayed, conditional)
missing module named 'ck4inductor.universal_gemm' - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional)
missing module named ck4inductor - imported by torch._inductor.codegen.rocm.ck_universal_gemm_template (delayed, optional), torch._inductor.utils (delayed, optional), torch._inductor.codegen.rocm.ck_conv_template (optional)
missing module named halide - imported by torch._inductor.codecache (delayed, conditional), torch._inductor.runtime.halide_helpers (optional)
missing module named rfe - imported by torch._inductor.remote_cache (conditional)
missing module named hiredis - imported by redis.utils (optional), redis.connection (conditional), redis._parsers.hiredis (delayed)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.guards (top-level), torch._dynamo.eval_frame (top-level), torch._dynamo.types (top-level), torch._dynamo.convert_frame (top-level), torch._inductor.fx_passes.reinplace (top-level), torch._functorch._aot_autograd.input_output_analysis (top-level), torch._dynamo.decorators (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.cpp_builder (conditional), torch._inductor.codecache (conditional)
missing module named 'libfb.py' - imported by torch._inductor.compile_worker.subproc_pool (delayed, conditional), torch._inductor.codegen.rocm.compile_command (delayed, conditional), torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'ck4inductor.grouped_conv_fwd' - imported by torch._inductor.codegen.rocm.ck_conv_template (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named cutlass_generator - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named cutlass - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, optional)
missing module named 'triton._utils' - imported by torch._higher_order_ops.triton_kernel_wrap (delayed, conditional)
missing module named 'triton._C' - imported by torch._higher_order_ops.triton_kernel_wrap (conditional)
missing module named 'torch_xla.stablehlo' - imported by torch._functorch.fx_minifier (delayed)
missing module named 'torch.utils._config_typing' - imported by torch._inductor.config (conditional), torch._functorch.config (conditional), torch._dynamo.config (conditional)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named torch._dynamo.variables.symbolic_convert - imported by torch._dynamo.variables.base (conditional)
missing module named 'optree._C' - imported by torch._dynamo.polyfills.pytree (conditional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, conditional, optional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named libfb - imported by torch._inductor.config (conditional, optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named 'torch_xla.utils' - imported by torch._tensor (delayed, conditional)
missing module named torch.tensor - imported by torch (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch._softmax_backward_data - imported by torch (delayed), transformers.pytorch_utils (delayed)
missing module named torch.ScriptObject - imported by torch (delayed), torch.export.graph_signature (delayed)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.Size - imported by torch (top-level), torch.types (top-level), transformers.models.nemotron.modeling_nemotron (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.qscheme - imported by torch (top-level), torch.types (top-level)
missing module named torch.layout - imported by torch (top-level), torch.types (top-level)
missing module named torch.DispatchKey - imported by torch (top-level), torch.types (top-level)
missing module named torch.device - imported by torch (top-level), torch.types (top-level), torch.nn.modules.module (top-level), torch._library.infer_schema (top-level), torch.cuda (top-level), torch._inductor.graph (top-level), torch.distributed.nn.api.remote_module (top-level), transformers.models.blip.modeling_blip_text (top-level), torch.xpu (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), twisted.web._http2 (top-level), scrapy.core.http2.protocol (top-level), scrapy.core.http2.stream (top-level)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), twisted.web._http2 (top-level), scrapy.core.http2.protocol (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), twisted.web._http2 (top-level), scrapy.core.http2.protocol (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level), twisted.web._http2 (top-level), scrapy.core.http2.protocol (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level), pennylane.data.data_manager.progress._rich (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by atlas_news_insights_engine (optional), sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'mysql.connector' - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed, conditional, optional)
missing module named mysql - imported by sqlalchemy.dialects.mysql.mysqlconnector (delayed)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named 'pymysql.constants' - imported by sqlalchemy.dialects.mysql.aiomysql (delayed)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named 'gunicorn.arbiter' - imported by uvicorn.workers (top-level)
missing module named sentence_transformers - imported by atlas_news_insights_engine (optional)
missing module named dimod.constrained.ConstrainedQuadraticModel - imported by dimod.constrained (delayed), dimod.quadratic.quadratic_model (delayed), dimod.generators.binpacking (top-level), dimod.generators.knapsack (top-level), dimod.generators.magic_square (top-level), dimod.generators.multi_knapsack (top-level), dimod.generators.quadratic_assignment (top-level), dimod.higherorder.utils (top-level), dimod.reference.samplers.exact_solver (top-level)
missing module named dimod.binary.BinaryQuadraticModel - imported by dimod.binary (delayed), dimod.quadratic.quadratic_model (delayed), dimod.generators.satisfiability (top-level)
missing module named dimod.ConstrainedQuadraticModel - imported by dimod (conditional), dimod.quadratic.quadratic_model (conditional)
missing module named dimod.BinaryQuadraticModel - imported by dimod (conditional), dimod.quadratic.quadratic_model (conditional), dimod.core.structured (conditional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named pylatexenc - imported by qiskit.visualization.circuit._utils (delayed)
missing module named 'qiskit._accelerate.circuit' - imported by qiskit.circuit (top-level), qiskit.circuit.quantumcircuit (top-level), qiskit.circuit.parameter (top-level), qiskit.circuit.parameterexpression (top-level), qiskit.circuit.library.standard_gates.u (top-level), qiskit.circuit.classical.types.types (top-level), qiskit.circuit.classical.expr.expr (top-level), qiskit.circuit.controlflow.builder (top-level), qiskit.circuit.quantumcircuitdata (top-level), qiskit.circuit.delay (top-level), qiskit.dagcircuit.dagcircuit (top-level), qiskit.circuit.tools.pi_check (delayed, conditional, optional), qiskit.circuit.library.blueprintcircuit (top-level), qiskit.circuit.measure (top-level), qiskit.qasm3.exporter (top-level), qiskit.circuit.annotation (top-level), qiskit.circuit.barrier (top-level), qiskit.dagcircuit.dagnode (top-level), qiskit.circuit.library.standard_gates.p (top-level), qiskit.circuit.library.standard_gates.u1 (top-level), qiskit.circuit.library.standard_gates.u2 (top-level), qiskit.circuit.library.standard_gates.u3 (top-level), qiskit.circuit.library.standard_gates.x (top-level), qiskit.circuit.library.standard_gates.sx (top-level), qiskit.circuit.library.standard_gates.rz (top-level), qiskit.circuit.reset (top-level), qiskit.circuit.library.standard_gates.s (top-level), qiskit.circuit.library.standard_gates.y (top-level), qiskit.circuit.library.standard_gates.z (top-level), qiskit.circuit.library.standard_gates.h (top-level), qiskit.circuit.library.standard_gates.i (top-level), qiskit.circuit.library.standard_gates.r (top-level), qiskit.circuit.library.standard_gates.rx (top-level), qiskit.circuit.library.standard_gates.rxx (top-level), qiskit.circuit.library.standard_gates.ry (top-level), qiskit.circuit.library.standard_gates.ryy (top-level), qiskit.circuit.library.standard_gates.rzz (top-level), qiskit.circuit.library.standard_gates.rzx (top-level), qiskit.circuit.library.standard_gates.xx_minus_yy (top-level), qiskit.circuit.library.standard_gates.xx_plus_yy (top-level), qiskit.circuit.library.standard_gates.ecr (top-level), qiskit.circuit.library.standard_gates.swap (top-level), qiskit.circuit.library.standard_gates.iswap (top-level), qiskit.circuit.library.standard_gates.dcx (top-level), qiskit.circuit.library.standard_gates.t (top-level), qiskit.circuit.library.standard_gates.global_phase (top-level)
missing module named 'qiskit._accelerate.sparse_observable' - imported by qiskit.quantum_info (top-level), qiskit.quantum_info.operators.symplectic.sparse_pauli_op (top-level)
missing module named 'qiskit._accelerate.sparse_pauli_op' - imported by qiskit.quantum_info.operators.symplectic.sparse_pauli_op (top-level)
missing module named 'qiskit._accelerate.synthesis' - imported by qiskit.synthesis.multi_controlled.mcmt_vchain (top-level), qiskit.synthesis.multi_controlled.mcx_synthesis (top-level), qiskit.synthesis.discrete_basis.solovay_kitaev (top-level), qiskit.synthesis.discrete_basis.generate_basis_approximations (top-level), qiskit.synthesis.linear.cnot_synth (top-level), qiskit.synthesis.linear.linear_matrix_utils (top-level), qiskit.synthesis.linear.linear_depth_lnn (top-level), qiskit.synthesis.linear_phase.cz_depth_lnn (top-level), qiskit.synthesis.linear_phase.cx_cz_depth_lnn (top-level), qiskit.synthesis.clifford.clifford_decompose_bm (top-level), qiskit.synthesis.clifford.clifford_decompose_greedy (top-level), qiskit.synthesis.evolution.pauli_network (top-level), qiskit.synthesis.permutation.permutation_lnn (top-level), qiskit.synthesis.permutation.permutation_full (top-level), qiskit.synthesis.permutation.permutation_reverse_lnn (top-level), qiskit.synthesis.qft.qft_decompose_lnn (top-level), qiskit.synthesis.permutation.permutation_utils (top-level), qiskit.quantum_info.operators.symplectic.random (top-level)
missing module named 'qiskit._accelerate.sampled_exp_val' - imported by qiskit.result.sampled_expval (top-level)
missing module named 'qiskit._accelerate.pauli_expval' - imported by qiskit.quantum_info.states.densitymatrix (top-level), qiskit.quantum_info.states.statevector (top-level)
missing module named 'qiskit._accelerate.pauli_lindblad_map' - imported by qiskit.quantum_info (top-level)
missing module named 'qiskit._accelerate.filter_op_nodes' - imported by qiskit.transpiler.passes.utils.filter_op_nodes (top-level)
missing module named 'qiskit._accelerate.gates_in_basis' - imported by qiskit.transpiler.passes.utils.gates_basis (top-level)
missing module named 'qiskit._accelerate.barrier_before_final_measurement' - imported by qiskit.transpiler.passes.utils.barrier_before_final_measurements (top-level)
missing module named 'qiskit._accelerate.gate_direction' - imported by qiskit.transpiler.passes.utils.check_gate_direction (top-level), qiskit.transpiler.passes.utils.gate_direction (top-level)
missing module named 'qiskit._accelerate.high_level_synthesis' - imported by qiskit.transpiler.passes.synthesis.high_level_synthesis (top-level), qiskit.transpiler.passes.synthesis.hls_plugins (top-level)
missing module named 'qiskit._accelerate.unitary_synthesis' - imported by qiskit.transpiler.passes.synthesis.unitary_synthesis (top-level)
missing module named 'qiskit._accelerate.split_2q_unitaries' - imported by qiskit.transpiler.passes.optimization.split_2q_unitaries (top-level)
missing module named 'qiskit._accelerate.remove_identity_equiv' - imported by qiskit.transpiler.passes.optimization.remove_identity_equiv (top-level)
missing module named 'qiskit._accelerate.inverse_cancellation' - imported by qiskit.transpiler.passes.optimization.inverse_cancellation (top-level)
missing module named 'qiskit._accelerate.remove_diagonal_gates_before_measure' - imported by qiskit.transpiler.passes.optimization.remove_diagonal_gates_before_measure (top-level)
missing module named 'qiskit._accelerate.commutation_checker' - imported by qiskit.transpiler.passes.optimization.commutative_cancellation (top-level), qiskit.circuit.commutation_checker (top-level)
missing module named 'qiskit._accelerate.commutation_analysis' - imported by qiskit.transpiler.passes.optimization.commutation_analysis (top-level)
missing module named 'qiskit._accelerate.consolidate_blocks' - imported by qiskit.transpiler.passes.optimization.consolidate_blocks (top-level)
missing module named 'qiskit._accelerate.optimize_1q_gates' - imported by qiskit.transpiler.passes.optimization.optimize_1q_gates (top-level)
missing module named 'qiskit._accelerate.equivalence' - imported by qiskit.circuit.equivalence (top-level)
missing module named 'qiskit._accelerate.basis_translator' - imported by qiskit.transpiler.passes.basis.basis_translator (top-level)
missing module named 'qiskit._accelerate.nlayout' - imported by qiskit.transpiler.passes.layout.sabre_layout (top-level), qiskit.transpiler.passes.routing.sabre_swap (top-level), qiskit.transpiler.passes.layout.vf2_utils (top-level)
missing module named 'qiskit._accelerate.sabre' - imported by qiskit.transpiler.passes.layout.sabre_layout (top-level), qiskit.transpiler.passes.routing.sabre_swap (top-level)
missing module named 'qiskit._accelerate.error_map' - imported by qiskit.transpiler.passes.layout.vf2_utils (top-level), qiskit.transpiler.passes.layout.sabre_pre_layout (top-level)
missing module named 'qiskit._accelerate.vf2_layout' - imported by qiskit.transpiler.passes.layout.vf2_layout (top-level)
missing module named constraint - imported by qiskit.transpiler.passes.layout.csp_layout (delayed), qiskit.transpiler.passes.layout._csp_custom_solver (conditional)
missing module named 'qiskit._accelerate.dense_layout' - imported by qiskit.transpiler.passes.layout.dense_layout (top-level)
missing module named 'qiskit._accelerate.circuit_duration' - imported by qiskit.circuit.quantumcircuit (top-level)
missing module named 'qiskit._accelerate.cos_sin_decomp' - imported by qiskit.synthesis.unitary.qsd (top-level)
missing module named 'qiskit._accelerate.two_qubit_decompose' - imported by qiskit.synthesis.unitary.qsd (top-level)
missing module named 'qiskit._accelerate.circuit_library' - imported by qiskit.circuit.library.data_preparation.pauli_feature_map (top-level), qiskit.circuit.library.n_local.n_local (top-level), qiskit.circuit.library.n_local.pauli_two_design (top-level), qiskit.synthesis.evolution.product_formula (top-level), qiskit.circuit.library.n_local.evolved_operator_ansatz (top-level), qiskit.circuit.library.quantum_volume (top-level), qiskit.circuit.library.iqp (top-level)
missing module named qiskit.circuit.library.MCPhaseGate - imported by qiskit.circuit.library (delayed, conditional), qiskit.circuit._add_control (delayed, conditional)
missing module named qiskit.circuit.library.GlobalPhaseGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.synthesis.hls_plugins (top-level)
missing module named qiskit.circuit.library.C4XGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.synthesis.hls_plugins (top-level)
missing module named qiskit.circuit.library.C3XGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.synthesis.hls_plugins (top-level)
missing module named qiskit.circuit.library.MCXGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.synthesis.hls_plugins (top-level)
missing module named qiskit.circuit.library.HGate - imported by qiskit.circuit.library (delayed), qiskit.transpiler.passes.scheduling.padding.context_aware_dynamical_decoupling (delayed)
missing module named qiskit.circuit.library.XGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.scheduling.padding.context_aware_dynamical_decoupling (top-level), qiskit.synthesis.boolean.boolean_expression_synth (top-level)
missing module named qiskit.circuit.library.ECRGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.scheduling.padding.context_aware_dynamical_decoupling (top-level)
missing module named qiskit.circuit.library.CXGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.scheduling.padding.context_aware_dynamical_decoupling (top-level)
missing module named qiskit.circuit.library.IGate - imported by qiskit.circuit.library (top-level), qiskit.synthesis.discrete_basis.solovay_kitaev (top-level)
missing module named qiskit.circuit.library.SdgGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.optimization.optimize_clifford_t (top-level)
missing module named qiskit.circuit.library.SGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.optimization.optimize_clifford_t (top-level)
missing module named qiskit.circuit.library.SwapGate - imported by qiskit.circuit.library (top-level), qiskit.transpiler.passes.routing.star_prerouting (top-level)
missing module named 'pylatexenc.latex2text' - imported by qiskit.visualization.circuit.matplotlib (delayed)
missing module named qiskit_qasm3_import - imported by qiskit.qasm3 (delayed)
missing module named 'qiskit._accelerate.qasm3' - imported by qiskit.qasm3 (top-level)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named cvxopt - imported by statsmodels.stats._knockoff (delayed, optional), statsmodels.regression.linear_model (delayed, optional)
missing module named statsmodels.sandbox.stats.ex_multicomp - imported by statsmodels.sandbox.stats.multicomp (conditional)
missing module named 'qiskit._accelerate.converters' - imported by qiskit.converters.circuit_to_dag (top-level), qiskit.converters.dag_to_circuit (top-level)
missing module named 'qiskit._accelerate.target' - imported by qiskit.transpiler.target (top-level)
missing module named 'qiskit._accelerate.twirling' - imported by qiskit.circuit.twirling (top-level)
missing module named 'qiskit._accelerate.qasm2' - imported by qiskit.qasm2.parse (top-level)
missing module named 'jax.tree_util' - imported by pennylane._grad (delayed), pennylane.pytrees.pytrees (optional), pennylane.pulse.parametrized_hamiltonian_pytree (top-level)
missing module named funcsigs - imported by autograd.differential_operators (delayed)
missing module named jaxlib - imported by pennylane.typing (delayed, conditional)
missing module named pennylane.capture.QmlPrimitive - imported by pennylane.capture (top-level), pennylane.workflow._capture_qnode (top-level), pennylane.allocation (optional)
missing module named 'jax.interpreters' - imported by pennylane.math.utils (delayed, conditional), pennylane.capture.dynamic_shapes (optional), pennylane.workflow._capture_qnode (top-level), pennylane.devices.qubit.jaxpr_adjoint (top-level)
missing module named cotengra - imported by autoray.lazy.core (delayed, optional)
missing module named fa2 - imported by autoray.lazy.draw (delayed, conditional)
missing module named 'jax.extend' - imported by pennylane.capture.custom_primitives (top-level), pennylane.transforms.optimization.merge_rotations (delayed, optional), pennylane.transforms.optimization.merge_amplitude_embedding (delayed, optional)
missing module named pennylane.ops.X - imported by pennylane.ops (top-level), pennylane.templates.subroutines.select (top-level), pennylane.qaoa.mixers (top-level), pennylane.qaoa.cycle (top-level), pennylane.spin.spin_hamiltonian (top-level)
missing module named pennylane.ops.Sum - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.conversion (top-level), pennylane.transforms.split_non_commuting (top-level), pennylane.pulse.parametrized_hamiltonian (top-level), pennylane.ops.functions.generator (top-level), pennylane.devices.qubit.measure (top-level), pennylane.devices.qubit.sampling (top-level), pennylane.devices._legacy_device (top-level), pennylane.devices.qubit_mixed.measure (top-level), pennylane.devices.qubit_mixed.sampling (top-level), pennylane.qchem.convert_openfermion (top-level), pennylane.devices.qutrit_mixed.measure (top-level), pennylane.devices.qutrit_mixed.sampling (top-level), pennylane.templates.subroutines.qdrift (top-level), pennylane.templates.subroutines.trotter (top-level)
missing module named pennylane.ops.SProd - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level), pennylane.transforms.split_non_commuting (top-level), pennylane.ops.functions.equal (top-level), pennylane.ops.functions.generator (top-level), pennylane.devices.qubit.sampling (top-level), pennylane.devices._legacy_device (top-level)
missing module named pennylane.ops.Prod - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level), pennylane.transforms.split_non_commuting (top-level), pennylane.ops.functions.generator (top-level), pennylane.devices.qubit.sampling (top-level), pennylane.devices._legacy_device (top-level), pennylane.gradients.parameter_shift (top-level), pennylane.qaoa.layers (top-level)
missing module named pennylane.ops.PauliZ - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level), pennylane.templates.subroutines.grover (top-level)
missing module named pennylane.ops.PauliY - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level)
missing module named pennylane.ops.PauliX - imported by pennylane.ops (top-level), pennylane.pauli.pauli_arithmetic (top-level), pennylane.pauli.utils (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level)
missing module named pennylane.ops.Conditional - imported by pennylane.ops (top-level), pennylane.devices.qubit.apply_operation (top-level), pennylane.ops.functions.equal (top-level), pennylane.drawer.drawable_layers (top-level), pennylane.drawer.utils (top-level), pennylane.drawer._add_obj (top-level)
missing module named stim - imported by pennylane.devices.default_clifford (optional)
missing module named 'mpi4py.futures' - imported by pennylane.concurrency.executors.external.mpi (delayed)
missing module named mpi4py - imported by pennylane.concurrency.executors.external.mpi (delayed)
missing module named pennylane.ops.Exp - imported by pennylane.ops (top-level), pennylane.ops.functions.equal (top-level), pennylane.noise.conditionals (top-level)
missing module named pennylane.ops.Controlled - imported by pennylane.ops (top-level), pennylane.ops.functions.equal (top-level), pennylane.drawer.utils (top-level), pennylane.drawer._add_obj (top-level), pennylane.noise.conditionals (top-level)
missing module named pennylane.ops.Adjoint - imported by pennylane.ops (top-level), pennylane.transforms.decompositions.clifford_t_transform (top-level), pennylane.ops.functions.equal (top-level), pennylane.noise.conditionals (top-level)
missing module named pennylane.ops.StatePrep - imported by pennylane.ops (top-level), pennylane.templates.embeddings.amplitude (top-level), pennylane.gradients.adjoint_metric_tensor (top-level)
missing module named pennylane.ops.PauliRot - imported by pennylane.ops (top-level), pennylane.templates.subroutines.arbitrary_unitary (top-level), pennylane.gradients.pulse_gradient (top-level), pennylane.gradients.pulse_gradient_odegen (top-level), pennylane.templates.subroutines.approx_time_evolution (top-level), pennylane.optimize.riemannian_gradient (top-level)
missing module named kahypar - imported by pennylane.qcut.kahypar (delayed, optional)
missing module named catalyst - imported by pennylane.optimize.qng_qjit (delayed, conditional)
missing module named pennylane.ops.RZ - imported by pennylane.ops (top-level), pennylane.templates.embeddings.angle (top-level), pennylane.templates.embeddings.iqp (top-level), pennylane.templates.embeddings.qaoaembedding (top-level), pennylane.templates.subroutines.fermionic_double_excitation (top-level), pennylane.templates.subroutines.fermionic_single_excitation (top-level), pennylane.templates.layers.particle_conserving_u2 (top-level), pennylane.templates.layers.random (top-level), pennylane.optimize.rotoselect (top-level)
missing module named pennylane.ops.RY - imported by pennylane.ops (top-level), pennylane.templates.embeddings.angle (top-level), pennylane.templates.embeddings.qaoaembedding (top-level), pennylane.templates.layers.random (top-level), pennylane.templates.layers.simplified_two_design (top-level), pennylane.optimize.rotoselect (top-level)
missing module named pennylane.ops.RX - imported by pennylane.ops (top-level), pennylane.templates.embeddings.angle (top-level), pennylane.templates.embeddings.qaoaembedding (top-level), pennylane.templates.layers.basic_entangler (top-level), pennylane.templates.subroutines.fermionic_double_excitation (top-level), pennylane.templates.subroutines.fermionic_single_excitation (top-level), pennylane.templates.layers.random (top-level), pennylane.optimize.rotoselect (top-level)
missing module named pennylane.ops.Evolution - imported by pennylane.ops (top-level), pennylane.ops.functions.evolve (top-level)
missing module named pennylane.ops.Pow - imported by pennylane.ops (top-level), pennylane.ops.functions.equal (top-level)
missing module named pennylane.ops.CompositeOp - imported by pennylane.ops (top-level), pennylane.transforms.diagonalize_measurements (top-level), pennylane.ops.functions.equal (top-level)
missing module named pennylane.capture.PlxprInterpreter - imported by pennylane.capture (delayed, optional), pennylane.transforms.optimization.cancel_inverses (delayed, optional), pennylane.transforms.optimization.commute_controlled (delayed, optional), pennylane.transforms.optimization.merge_rotations (delayed, optional), pennylane.transforms.optimization.merge_amplitude_embedding (delayed, optional), pennylane.transforms.optimization.single_qubit_fusion (delayed, optional), pennylane.transforms.defer_measurements (delayed, optional), pennylane.transforms.unitary_to_rot (delayed, optional)
missing module named 'pyzx.graph' - imported by pennylane.transforms.zx.converter (delayed, optional)
missing module named 'pyzx.circuit' - imported by pennylane.transforms.zx.converter (delayed, optional)
missing module named pyzx - imported by pennylane.transforms.zx.converter (delayed, optional)
missing module named pennylane.templates.QFT - imported by pennylane.templates (top-level), pennylane.transforms.qmc (top-level)
missing module named pennylane.ops.SymbolicOp - imported by pennylane.ops (top-level), pennylane.transforms.diagonalize_measurements (top-level)
missing module named 'qualtran.bloqs' - imported by pennylane.io.qualtran_io (delayed, optional)
missing module named 'qualtran._infra' - imported by pennylane.io.qualtran_io (optional)
missing module named qualtran - imported by pennylane.io.qualtran_io (optional)
missing module named 'openqasm3.visitor' - imported by pennylane.io.qasm_interpreter (top-level)
missing module named openqasm3 - imported by pennylane.io.io (optional), pennylane.io.qasm_interpreter (top-level)
missing module named pennylane.ops.CNOT - imported by pennylane.ops (top-level), pennylane.templates.layers.basic_entangler (top-level), pennylane.templates.subroutines.fermionic_double_excitation (top-level), pennylane.templates.subroutines.fermionic_single_excitation (top-level), pennylane.templates.subroutines.select (top-level), pennylane.templates.layers.particle_conserving_u1 (top-level), pennylane.templates.layers.particle_conserving_u2 (top-level), pennylane.templates.layers.random (top-level), pennylane.templates.layers.strongly_entangling (top-level)
missing module named pennylane.ops.MultiControlledX - imported by pennylane.ops (top-level), pennylane.templates.subroutines.grover (top-level)
missing module named pennylane.ops.Hadamard - imported by pennylane.ops (top-level), pennylane.templates.subroutines.fermionic_double_excitation (top-level), pennylane.templates.subroutines.fermionic_single_excitation (top-level), pennylane.templates.subroutines.grover (top-level)
missing module named pennylane.ops.BasisState - imported by pennylane.ops (top-level), pennylane.templates.subroutines.uccsd (top-level), pennylane.templates.subroutines.all_singles_doubles (top-level)
missing module named pennylane.ops.QubitUnitary - imported by pennylane.ops (top-level), pennylane.templates.subroutines.qmc (top-level)
missing module named pennylane.ops.SWAP - imported by pennylane.ops (top-level), pennylane.templates.subroutines.permute (top-level), pennylane.templates.swapnetworks.ccl2 (top-level)
missing module named pennylane.ops.FermionicSWAP - imported by pennylane.ops (top-level), pennylane.templates.swapnetworks.ccl2 (top-level)
missing module named pennylane.ops.Rot - imported by pennylane.ops (top-level), pennylane.templates.layers.strongly_entangling (top-level)
missing module named pennylane.ops.CZ - imported by pennylane.ops (top-level), pennylane.templates.layers.particle_conserving_u1 (top-level), pennylane.templates.layers.simplified_two_design (top-level)
missing module named pennylane.ops.CRX - imported by pennylane.ops (top-level), pennylane.templates.layers.particle_conserving_u2 (top-level)
missing module named pennylane.ops.PhaseShift - imported by pennylane.ops (top-level), pennylane.templates.layers.particle_conserving_u1 (top-level)
missing module named pennylane.ops.CRot - imported by pennylane.ops (top-level), pennylane.templates.layers.particle_conserving_u1 (top-level)
missing module named pennylane.ops.OrbitalRotation - imported by pennylane.ops (top-level), pennylane.templates.layers.gate_fabric (top-level)
missing module named pennylane.ops.DoubleExcitation - imported by pennylane.ops (top-level), pennylane.templates.layers.gate_fabric (top-level)
missing module named pennylane.ops.MultiRZ - imported by pennylane.ops (top-level), pennylane.templates.embeddings.iqp (top-level), pennylane.templates.embeddings.qaoaembedding (top-level)
missing module named pennylane.ops.H - imported by pennylane.ops (top-level), pennylane.templates.embeddings.iqp (top-level), pennylane.templates.embeddings.qaoaembedding (top-level)
missing module named pennylane.ops.LinearCombination - imported by pennylane.ops (top-level), pennylane.transforms.diagonalize_measurements (top-level), pennylane.pauli.pauli_interface (top-level), pennylane.pauli.conversion (top-level), pennylane.transforms.transpile (top-level), pennylane.ops.functions.generator (top-level), pennylane.devices.qubit.measure (top-level), pennylane.devices.qubit.sampling (top-level), pennylane.devices._legacy_device (top-level), pennylane.devices.qubit_mixed.measure (top-level), pennylane.devices.qubit_mixed.sampling (top-level), pennylane.qchem.convert_openfermion (top-level), pennylane.templates.subroutines.qdrift (top-level), pennylane (top-level), pennylane.qaoa.mixers (top-level), pennylane.qaoa.cycle (top-level), pennylane.qaoa.cost (top-level), pennylane.optimize.riemannian_gradient (top-level), pennylane.optimize.shot_adaptive (top-level), pennylane.noise.conditionals (top-level)
missing module named pennylane.ops.prod - imported by pennylane.ops (top-level), pennylane.gradients.parameter_shift (top-level), pennylane (top-level), pennylane.qaoa.mixers (top-level)
missing module named pennylane.ops.exp - imported by pennylane.ops (top-level), pennylane.gradients.pulse_gradient (top-level), pennylane (top-level)
missing module named pennylane.ops.ctrl - imported by pennylane.ops (top-level), pennylane.devices.qubit.dq_interpreter (top-level), pennylane.templates.subroutines.select (top-level), pennylane (top-level), pennylane.noise.conditionals (top-level)
missing module named 'pyscf.geomopt' - imported by pennylane.qchem.vibrational.vibrational_class (delayed)
missing module named geometric - imported by pennylane.qchem.vibrational.vibrational_class (delayed, optional)
missing module named 'pyscf.hessian' - imported by pennylane.qchem.vibrational.vibrational_class (delayed)
missing module named pubchempy - imported by pennylane.qchem.structure (delayed, optional)
missing module named pyscf - imported by pennylane.qchem.openfermion_pyscf (delayed, optional)
missing module named openfermionpyscf - imported by pennylane.qchem.openfermion_pyscf (delayed, conditional, optional)
missing module named openfermion - imported by pennylane.qchem.convert (delayed, optional), pennylane.qchem.convert_openfermion (delayed, optional), pennylane.qchem.openfermion_pyscf (delayed, optional)
missing module named optax - imported by pennylane.qchem.factorization (optional)
missing module named basis_set_exchange - imported by pennylane.qchem.basis_data (delayed, optional)
missing module named pennylane.PauliX - imported by pennylane (top-level), pennylane.transforms.qmc (top-level)
missing module named pennylane.MultiControlledX - imported by pennylane (top-level), pennylane.transforms.qmc (top-level)
missing module named pennylane.Hadamard - imported by pennylane (top-level), pennylane.transforms.qmc (top-level)
missing module named pennylane.CZ - imported by pennylane (top-level), pennylane.transforms.qmc (top-level)
missing module named pennylane.AmplitudeEmbedding - imported by pennylane (top-level), pennylane.transforms.optimization.merge_amplitude_embedding (top-level)
missing module named astn - imported by gast.ast2 (top-level)
missing module named 'tensorflow.linalg' - imported by pennylane.math.multi_dispatch (delayed, conditional)
missing module named 'jax.scipy' - imported by pennylane.math.multi_dispatch (delayed, conditional)
missing module named 'jax.errors' - imported by pennylane.math.single_dispatch (delayed)
missing module named qutip - imported by atlas_quantum_optimizer (optional)
missing module named priority - imported by twisted.web._http2 (top-level)
missing module named 'h2.errors' - imported by twisted.web._http2 (top-level), scrapy.core.http2.protocol (top-level), scrapy.core.http2.stream (top-level)
missing module named google.cloud.storage - imported by google.cloud (delayed), scrapy.utils.test (delayed), scrapy.extensions.feedexport (delayed), scrapy.pipelines.files (delayed)
missing module named subunit - imported by twisted.trial.reporter (optional)
missing module named ptpython - imported by scrapy.utils.console (delayed)
missing module named bpython - imported by scrapy.utils.console (delayed)
missing module named robotexclusionrulesparser - imported by scrapy.robotstxt (delayed)
missing module named 'botocore.session' - imported by scrapy.pipelines.files (delayed)
missing module named 'boto3.session' - imported by scrapy.extensions.feedexport (delayed, optional)
missing module named hpack - imported by scrapy.core.http2.stream (conditional)
missing module named 'botocore.awsrequest' - imported by scrapy.core.downloader.handlers.s3 (delayed, conditional)
missing module named 'botocore.credentials' - imported by scrapy.core.downloader.handlers.s3 (delayed)
missing module named 'botocore.auth' - imported by scrapy.core.downloader.handlers.s3 (delayed)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named speech_recognition - imported by atlas_video_processor (optional)
missing module named causalml - imported by atlas_causal_reasoning (optional)
missing module named econml - imported by atlas_causal_reasoning (optional)
missing module named dowhy - imported by atlas_causal_reasoning (optional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), fastapi.openapi.utils (top-level), C:\Users\<USER>\Desktop\atlas_v4_enhanced10 - Copy\atlas_v4_enhanced\atlas_server.py (top-level), pydantic_settings.sources.base (top-level), pydantic_settings.sources.utils (top-level), pydantic_settings.sources.providers.cli (top-level), models (top-level), atlas_grok_integration (optional), huggingface_hub._webhooks_payload (conditional), openai.resources.beta.realtime.realtime (top-level)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
