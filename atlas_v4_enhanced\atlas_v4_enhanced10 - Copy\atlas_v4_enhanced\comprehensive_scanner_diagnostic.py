#!/usr/bin/env python3
"""
Comprehensive A.T.L.A.S. Lee Method Scanner Diagnostic
Diagnoses and fixes pattern detection, data feed, and alert generation issues
"""

import asyncio
import json
import time
import traceback
from datetime import datetime, time as dt_time
import pytz

async def comprehensive_scanner_diagnostic():
    """Comprehensive diagnostic of all scanner components"""
    try:
        print('🔍 COMPREHENSIVE A.T.L.A.S. LEE METHOD SCANNER DIAGNOSTIC')
        print('=' * 70)
        
        # Import all required modules
        print('📦 Testing imports...')
        from atlas_realtime_scanner import AtlasRealtimeScanner
        from atlas_lee_method import LeeMethodScanner
        from atlas_market_core import AtlasMarketEngine
        from atlas_alert_manager import AtlasAlertManager
        from sp500_symbols import get_sp500_symbols, get_high_volume_symbols
        from config import get_api_config
        from atlas_enhanced_market_data import enhanced_market_data
        print('✅ All imports successful')
        
        # 1. PATTERN DETECTION ISSUES DIAGNOSTIC
        print('\n🎯 1. PATTERN DETECTION DIAGNOSTIC')
        print('-' * 50)
        
        # Initialize Lee Method scanner
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        market_engine = AtlasMarketEngine()
        await market_engine.initialize()
        
        lee_scanner = LeeMethodScanner(fmp_api_key, market_engine)
        await lee_scanner.initialize()
        
        # Test pattern detection on multiple symbols
        test_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'TSLA']
        pattern_results = {}
        
        for symbol in test_symbols:
            try:
                print(f'   Testing {symbol}...')
                start_time = time.time()
                
                # Test direct Lee Method scanning
                signal = await lee_scanner.scan_symbol(symbol)
                detection_time = (time.time() - start_time) * 1000
                
                pattern_results[symbol] = {
                    'signal_found': signal is not None,
                    'detection_time_ms': detection_time,
                    'signal_details': signal.__dict__ if signal else None
                }
                
                if signal:
                    print(f'   ✅ {symbol}: Pattern detected! Bars: {signal.consecutive_bars}, Confidence: {signal.confidence:.2f}')
                else:
                    print(f'   ⚪ {symbol}: No pattern detected ({detection_time:.1f}ms)')
                    
            except Exception as e:
                pattern_results[symbol] = {'error': str(e)}
                print(f'   ❌ {symbol}: Error - {str(e)[:60]}')
        
        # 2. DATA FEED PROBLEMS DIAGNOSTIC
        print('\n📡 2. DATA FEED DIAGNOSTIC')
        print('-' * 50)
        
        # Test market data availability
        data_sources = ['FMP', 'Alpaca', 'Enhanced Market Data']
        data_status = {}
        
        for source in data_sources:
            try:
                if source == 'FMP':
                    # Test FMP API
                    fmp_data = await market_engine.get_quote('AAPL')
                    data_status['FMP'] = {
                        'status': 'connected' if fmp_data else 'no_data',
                        'sample_data': fmp_data
                    }
                    
                elif source == 'Alpaca':
                    # Test Alpaca API
                    alpaca_data = await market_engine.get_latest_trade('AAPL')
                    data_status['Alpaca'] = {
                        'status': 'connected' if alpaca_data else 'no_data',
                        'sample_data': alpaca_data
                    }
                    
                elif source == 'Enhanced Market Data':
                    # Test enhanced market data
                    enhanced_data = await enhanced_market_data.get_historical_data('AAPL', timeframe='1d')
                    data_status['Enhanced'] = {
                        'status': 'connected' if enhanced_data and not enhanced_data.data.empty else 'no_data',
                        'data_points': len(enhanced_data.data) if enhanced_data and not enhanced_data.data.empty else 0
                    }
                    
            except Exception as e:
                data_status[source] = {'status': 'error', 'error': str(e)}
        
        for source, status in data_status.items():
            if status['status'] == 'connected':
                print(f'   ✅ {source}: Connected and providing data')
            elif status['status'] == 'no_data':
                print(f'   ⚠️  {source}: Connected but no data available')
            else:
                print(f'   ❌ {source}: Error - {status.get("error", "Unknown")}')
        
        # 3. MARKET HOURS VALIDATION
        print('\n🕐 3. MARKET HOURS VALIDATION')
        print('-' * 50)
        
        ct_tz = pytz.timezone('US/Central')
        current_ct = datetime.now(ct_tz)
        current_time_ct = current_ct.time()
        
        market_open = dt_time(8, 30)  # 8:30 AM CT
        market_close = dt_time(15, 0)  # 3:00 PM CT
        is_weekday = current_ct.weekday() < 5
        
        market_hours_active = market_open <= current_time_ct <= market_close and is_weekday
        
        print(f'   Current Time (CT): {current_time_ct}')
        print(f'   Market Hours: {market_open} - {market_close} CT')
        print(f'   Is Weekday: {is_weekday}')
        print(f'   Market Hours Active: {market_hours_active}')
        
        # Test scanner market hours detection
        realtime_scanner = AtlasRealtimeScanner()
        scanner_should_scan = realtime_scanner._should_scan()
        print(f'   Scanner Should Scan: {scanner_should_scan}')
        
        if market_hours_active != scanner_should_scan:
            print('   ⚠️  Market hours detection inconsistency!')
        else:
            print('   ✅ Market hours detection consistent')
        
        # 4. ALERT GENERATION DIAGNOSTIC
        print('\n🚨 4. ALERT GENERATION DIAGNOSTIC')
        print('-' * 50)
        
        alert_manager = AtlasAlertManager()
        
        # Test alert generation with mock pattern
        mock_pattern = {
            'symbol': 'TEST_ALERT',
            'pattern_found': True,
            'consecutive_bars': 4,
            'decline_percent': -2.8,
            'confidence': 0.75,
            'current_price': 150.25,
            'signal_strength': 'STRONG'
        }
        
        alert_start = time.time()
        alert = await alert_manager.generate_lee_method_alert(
            symbol='TEST_ALERT',
            pattern_result=mock_pattern,
            market_data={}
        )
        alert_time = (time.time() - alert_start) * 1000
        
        if alert:
            print(f'   ✅ Alert generation successful ({alert_time:.2f}ms)')
            print(f'   Alert Priority: {alert.get("priority")}')
            print(f'   Alert Message: {alert.get("alert_message", "")[:60]}...')
        else:
            print('   ❌ Alert generation failed')
        
        # 5. SYMBOL COVERAGE DIAGNOSTIC
        print('\n📊 5. SYMBOL COVERAGE DIAGNOSTIC')
        print('-' * 50)
        
        sp500_symbols = get_sp500_symbols()
        high_volume_symbols = get_high_volume_symbols()
        
        print(f'   S&P 500 Symbols Available: {len(sp500_symbols)}')
        print(f'   High Volume Symbols: {len(high_volume_symbols)}')
        
        # Test scanner symbol configuration
        scanner_symbols = realtime_scanner.symbols if hasattr(realtime_scanner, 'symbols') else []
        priority_symbols = realtime_scanner.priority_symbols if hasattr(realtime_scanner, 'priority_symbols') else []
        
        print(f'   Scanner Active Symbols: {len(scanner_symbols)}')
        print(f'   Priority Symbols: {len(priority_symbols)}')
        
        if len(scanner_symbols) < 300:
            print('   ⚠️  Symbol coverage may be insufficient')
        else:
            print('   ✅ Symbol coverage adequate')
        
        # 6. CONFIDENCE THRESHOLDS DIAGNOSTIC
        print('\n🎚️ 6. CONFIDENCE THRESHOLDS DIAGNOSTIC')
        print('-' * 50)
        
        config = realtime_scanner.config
        print(f'   Min Confidence: {config.min_confidence}')
        print(f'   Scanner Sensitivity: {getattr(config, "sensitivity", "N/A")}')
        
        # Test with different confidence levels
        confidence_test_results = {}
        test_confidences = [0.5, 0.65, 0.75, 0.85]
        
        for conf in test_confidences:
            # Mock a pattern with specific confidence
            mock_result = {
                'symbol': 'CONF_TEST',
                'pattern_found': True,
                'confidence': conf,
                'consecutive_bars': 3,
                'signal_strength': 'MEDIUM' if conf >= 0.65 else 'WEAK'
            }
            
            # Check if this would pass the threshold
            would_alert = conf >= config.min_confidence
            confidence_test_results[conf] = would_alert
            
            status = '✅ Would alert' if would_alert else '❌ Below threshold'
            print(f'   Confidence {conf}: {status}')
        
        # 7. ASYNC PROCESSING ERRORS DIAGNOSTIC
        print('\n⚡ 7. ASYNC PROCESSING DIAGNOSTIC')
        print('-' * 50)
        
        # Test async operations
        async_test_results = []
        
        try:
            # Test concurrent scanning
            concurrent_symbols = ['SPY', 'QQQ', 'IWM']
            tasks = []
            
            for symbol in concurrent_symbols:
                task = asyncio.create_task(lee_scanner.scan_symbol(symbol))
                tasks.append(task)
            
            concurrent_start = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            concurrent_time = time.time() - concurrent_start
            
            successful_scans = sum(1 for r in results if not isinstance(r, Exception))
            print(f'   Concurrent Scans: {successful_scans}/{len(concurrent_symbols)} successful')
            print(f'   Concurrent Time: {concurrent_time:.2f}s')
            
            if successful_scans == len(concurrent_symbols):
                print('   ✅ Async processing working correctly')
            else:
                print('   ⚠️  Some async operations failed')
                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        print(f'      {concurrent_symbols[i]}: {str(result)[:50]}')
                        
        except Exception as e:
            print(f'   ❌ Async processing error: {str(e)}')
        
        # 8. REAL VS SIMULATED DATA DIAGNOSTIC
        print('\n🎲 8. REAL VS SIMULATED DATA DIAGNOSTIC')
        print('-' * 50)
        
        # Check for any fallback data usage
        real_data_test = await enhanced_market_data.get_historical_data('AAPL', timeframe='1d')
        
        if real_data_test and not real_data_test.data.empty:
            print('   ✅ Real market data available')
            print(f'   Data Source: {real_data_test.source}')
            print(f'   Data Points: {len(real_data_test.data)}')
            print(f'   Latest Data: {real_data_test.data.index[-1] if len(real_data_test.data) > 0 else "N/A"}')
        else:
            print('   ❌ No real market data available')
            print('   Scanner may be using fallback or no data')
        
        # SUMMARY AND RECOMMENDATIONS
        print('\n📋 DIAGNOSTIC SUMMARY & RECOMMENDATIONS')
        print('=' * 70)
        
        issues_found = []
        recommendations = []
        
        # Analyze results and provide recommendations
        if not any(r.get('signal_found', False) for r in pattern_results.values()):
            issues_found.append('No patterns detected in test symbols')
            recommendations.append('Check if market conditions have 3+ consecutive declining bars')
        
        if not market_hours_active:
            issues_found.append('Market is currently closed')
            recommendations.append('Scanner will activate during market hours (8:30 AM - 3:00 PM CT)')
        
        if not all(status.get('status') == 'connected' for status in data_status.values()):
            issues_found.append('Data feed connectivity issues')
            recommendations.append('Check API keys and network connectivity')
        
        if config.min_confidence > 0.7:
            issues_found.append('High confidence threshold may prevent alerts')
            recommendations.append('Consider lowering min_confidence to 0.65 for more sensitivity')
        
        print(f'Issues Found: {len(issues_found)}')
        for i, issue in enumerate(issues_found, 1):
            print(f'   {i}. {issue}')
        
        print(f'\nRecommendations: {len(recommendations)}')
        for i, rec in enumerate(recommendations, 1):
            print(f'   {i}. {rec}')
        
        if not issues_found:
            print('\n🎉 NO CRITICAL ISSUES FOUND - Scanner appears to be functioning correctly')
            print('   Pattern detection may be working as expected with current market conditions')
        
        return len(issues_found) == 0
        
    except Exception as e:
        print(f'❌ DIAGNOSTIC FAILED: {e}')
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(comprehensive_scanner_diagnostic())
    exit(0 if success else 1)
